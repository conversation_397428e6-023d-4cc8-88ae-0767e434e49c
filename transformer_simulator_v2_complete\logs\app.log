2025-05-23 23:28:40 - app - ERROR - <PERSON>rro inesperado ao processar módulo de callback losses: unexpected indent (losses.py, line 484) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 484
    try:
IndentationError: unexpected indent
2025-05-24 13:51:46 - __main__ - CRITICAL - ERRO CRÍTICO AO CRIAR LAYOUT [app.py:173]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 166, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\components\global_stores.py", line 53, in create_global_stores
    dcc.Store(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\development\base_component.py", line 425, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dcc\Store.py", line 75, in __init__
    super(Store, self).__init__(**args)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\development\base_component.py", line 143, in __init__
    raise TypeError(
TypeError: The `dcc.Store` component (version 2.17.1) with the ID "transformer-inputs-store" received an unexpected keyword argument: `persisted`
Allowed arguments: clear_data, data, id, modified_timestamp, storage_type
2025-05-24 13:51:46 - __main__ - CRITICAL - Erro inesperado durante a criação do layout: The `dcc.Store` component (version 2.17.1) with the ID "transformer-inputs-store" received an unexpected keyword argument: `persisted`
Allowed arguments: clear_data, data, id, modified_timestamp, storage_type [app.py:174]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 166, in <module>
    app.layout = create_main_layout(
                 ^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\layouts\main_layout.py", line 432, in create_main_layout
    global_stores = create_global_stores(app)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\components\global_stores.py", line 53, in create_global_stores
    dcc.Store(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\development\base_component.py", line 425, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dcc\Store.py", line 75, in __init__
    super(Store, self).__init__(**args)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\development\base_component.py", line 143, in __init__
    raise TypeError(
TypeError: The `dcc.Store` component (version 2.17.1) with the ID "transformer-inputs-store" received an unexpected keyword argument: `persisted`
Allowed arguments: clear_data, data, id, modified_timestamp, storage_type
2025-05-24 13:51:46 - __main__ - ERROR - Início do servidor abortado devido a falha na criação do layout ou registro de callbacks. [app.py:262]
2025-05-24 13:52:51 - app - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 215) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 215
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:52:58 - app - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 215) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 215
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:53:19 - app - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 257) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 257
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
