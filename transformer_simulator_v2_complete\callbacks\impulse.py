# callbacks/impulse.py
import logging
import math
import re
import warnings
import datetime # Para timestamp no store

import dash
import dash_bootstrap_components as dbc
import numpy as np
import plotly.graph_objects as go
from dash import Input, Output, State, html, ctx
from dash.exceptions import PreventUpdate
from scipy.fftpack import fft, fftfreq, ifft
from scipy.optimize import OptimizeWarning, curve_fit

# Importações da aplicação
from app import app # Importar instância da app
from config import colors # Para estilos

# Import constants/utils
from utils import constants as const
from utils.routes import ROUTE_IMPULSE, normalize_pathname
from utils.store_diagnostics import convert_numpy_types, is_json_serializable
from utils.callback_helpers import safe_float, safe_int

# --- Configuração do Logging ---
logger = logging.getLogger(__name__)
log = logger
if not logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# --- Supressão de Warnings ---
warnings.filterwarnings("ignore", category=OptimizeWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# --- Constantes Normativas e de Ensaio ---
# (Mantidas como no original)
LIGHTNING_IMPULSE_FRONT_TIME_NOM = 1.2
LIGHTNING_IMPULSE_TAIL_TIME_NOM = 50.0
SWITCHING_IMPULSE_PEAK_TIME_NOM = 250.0
SWITCHING_IMPULSE_TAIL_TIME_NOM = 2500.0
CHOPPED_IMPULSE_CHOP_TIME_MIN = 2.0
CHOPPED_IMPULSE_CHOP_TIME_MAX = 6.0
LIGHTNING_FRONT_TOLERANCE = 0.30
LIGHTNING_TAIL_TOLERANCE = 0.20
LIGHTNING_PEAK_TOLERANCE = 0.03
LIGHTNING_OVERSHOOT_MAX = 10.0
SWITCHING_PEAK_TOLERANCE = 0.03
SWITCHING_PEAK_TIME_TOLERANCE = 0.20
SWITCHING_TAIL_TOLERANCE = 0.60
SWITCHING_TIME_ABOVE_90_MIN = 200.0
SWITCHING_TIME_TO_ZERO_MIN = 1000.0
CHOPPED_FRONT_TOLERANCE = 0.30
CHOPPED_PEAK_TOLERANCE = 0.05
CHOPPED_UNDERSHOOT_MAX = 30.0

# --- Parâmetros Físicos e do Gerador ---
# (Mantidos como no original)
L_PER_STAGE_H = 5e-6
C_PER_STAGE = 1.5e-6
C_DIVIDER_HIGH_VOLTAGE = 600e-12
C_DIVIDER_LOW_VOLTAGE = 1200e-12
C_CHOPPING_GAP_PF = 600e-12
R_PARASITIC_OHM = 5.0

# --- Constantes Físicas ---
PI = np.pi
EPSILON_0 = 8.854e-12
MU_0 = 4 * PI * 1e-7

# --- Dados de referência para sugestão de resistores ---
# (Mantidos como no original)
RF_REFERENCE_DATA_LI = [(0.5, 500), (1, 350), (2, 220), (4, 140), (8, 90), (16, 60), (32, 40)]
RF_REFERENCE_DATA_SI = [(0.5, 5000), (1, 3500), (2, 2200), (4, 1400), (8, 900), (16, 600), (32, 400)]
RT_DEFAULT_PER_COLUMN = {"lightning": 100, "switching": 2500, "chopped": 120}

# --- Funções Auxiliares ---
# (Mantidas como no original, mas com logging ajustado)
def parse_resistor_expression(expression):
    if not expression or not isinstance(expression, str) or expression.strip() == "":
        log.warning("Expressão de resistor vazia ou inválida.")
        return float("inf"), {}
    expression = expression.lower().strip()
    special_components = {}
    cap_match = re.search(r"(\d+(?:\.\d+)?)\s*pf", expression)
    if cap_match:
        try:
            cap_value = float(cap_match.group(1)) * 1e-12
            special_components["coupling_capacitor"] = cap_value
            expression = re.sub(r"\|\|\s*\d+(?:\.\d+)?\s*pf", "", expression).strip()
            expression = re.sub(r"\d+(?:\.\d+)?\s*pf\s*\|\|", "", expression).strip()
            log.info(f"Capacitor {cap_value*1e12:.0f} pF detectado em Rf.")
        except ValueError: log.error(f"Erro ao converter capacitor em {expression}."); return float("inf"), {}
    try:
        expression_safe = expression.replace("||", "__parallel__")
        expression_safe = re.sub(r"(\d+(?:\.\d+)?)\s*k", r"(\1 * 1000)", expression_safe)
        expression_safe = re.sub(r"(\d+(?:\.\d+)?)\s*m", r"(\1 * 1000000)", expression_safe)
        def calculate_parallel(*args):
            args_float = [float(r) for r in args if safe_float(r) is not None and float(r) > 0]
            if not args_float: return float("inf")
            inv_sum = sum(1.0 / r for r in args_float)
            return 1.0 / inv_sum if inv_sum > 1e-12 else float("inf")
        safe_dict = {"__parallel__": calculate_parallel, "__builtins__": None}
        series_parts = expression_safe.split("+")
        total_resistance = 0
        if not series_parts or all(not part.strip() for part in series_parts): return float("inf"), special_components
        for part in series_parts:
            part = part.strip()
            if not part: continue
            if not re.match(r"^[\d\.\s\*\(\)/,__parallel__]+$", part): raise ValueError(f"Caracteres inválidos: {part}")
            try:
                part_value = eval(part, {"__builtins__": {}}, safe_dict)
                if part_value <= 0 or math.isinf(part_value): log.warning(f"Parte inválida: {part} -> {part_value}"); return float("inf"), special_components
                total_resistance += part_value
            except Exception as e: log.error(f"Erro ao avaliar: {part}, erro: {e}"); return float("inf"), special_components
        if total_resistance <= 0 or math.isinf(total_resistance): log.warning(f"Resistência total inválida: {total_resistance}"); return float("inf"), special_components
        return total_resistance, special_components
    except ZeroDivisionError: log.error(f"Divisão por zero em {expression}."); return float("inf"), special_components
    except Exception as e: log.error(f"Erro ao analisar {expression}: {e}"); return float("inf"), special_components

def get_generator_params(config_value):
    config = next((item for item in const.GENERATOR_CONFIGURATIONS if item["value"] == config_value), None)
    if config:
        energy_kj = config.get("energy_kj")
        try: energy_kj_float = float(energy_kj) if energy_kj is not None else 0.0
        except (ValueError, TypeError): log.warning(f"Energia inválida para {config_value}: {energy_kj}. Usando 0."); energy_kj_float = 0.0
        return config["stages"], config["parallel"], config["max_voltage_kv"], energy_kj_float
    log.warning(f"Config {config_value} não encontrada. Usando 12S-1P.")
    default_config = next((item for item in const.GENERATOR_CONFIGURATIONS if item["value"] == "12S-1P"), None)
    if default_config:
        energy_kj = default_config.get("energy_kj"); energy_kj_float = float(energy_kj) if energy_kj is not None else 360.0
        return default_config["stages"], default_config["parallel"], default_config["max_voltage_kv"], energy_kj_float
    else: return 12, 1, 2400, 360.0

def get_divider_capacitance(generator_config_value):
    try:
        _, _, max_voltage_kv, _ = get_generator_params(generator_config_value)
        return C_DIVIDER_LOW_VOLTAGE if max_voltage_kv <= 1200 else C_DIVIDER_HIGH_VOLTAGE
    except Exception as e: log.error(f"Erro ao obter C divisor: {e}. Usando padrão."); return C_DIVIDER_HIGH_VOLTAGE

def calculate_effective_gen_params(n_stages, n_parallel):
    if n_stages <= 0 or n_parallel <= 0: log.error("Estágios/paralelo devem ser > 0."); return 0
    c_gen_effective = (C_PER_STAGE * n_parallel) / n_stages
    log.debug(f"Cg_eff: {c_gen_effective*1e6:.3f} µF ({n_stages}S-{n_parallel}P)")
    return c_gen_effective

def calculate_transformer_inductance(voltage_kv, power_mva, impedance_percent, freq_hz=60):
    if voltage_kv is None or power_mva is None or impedance_percent is None or freq_hz is None: return 0.05
    try:
        voltage_v = float(voltage_kv) * 1000; power_va = float(power_mva) * 1e6
        impedance_pu = float(impedance_percent) / 100.0; freq_hz = float(freq_hz)
        if voltage_v <= 0 or power_va <= 0 or freq_hz <= 0: return 0.05
        omega = 2 * np.pi * freq_hz; z_base = (voltage_v**2) / power_va
        z_cc_ohm = z_base * impedance_pu; l_cc = z_cc_ohm / omega
        log.info(f"Indutância trafo: {l_cc:.4f} H")
        return l_cc
    except (ValueError, TypeError, ZeroDivisionError) as e: log.error(f"Erro calc indutância: {e}. Usando 0.05H."); return 0.05

def calculate_circuit_efficiency(n_stages, n_parallel, c_test_object_pf, c_stray_pf, impulse_type, generator_config_value):
    try:
        c_gen_effective = calculate_effective_gen_params(n_stages, n_parallel)
        c_divider_f = get_divider_capacitance(generator_config_value)
        c_test_object_f = c_test_object_pf * 1e-12 if c_test_object_pf is not None else 0
        c_stray_f = c_stray_pf * 1e-12 if c_stray_pf is not None else 0
        c_load_extra_f = C_CHOPPING_GAP_PF * 1e-12 if impulse_type == "chopped" else 0
        c_load = c_test_object_f + c_divider_f + c_load_extra_f + c_stray_f
        circuit_efficiency = c_gen_effective / (c_gen_effective + c_load) if (c_gen_effective + c_load) > 1e-15 else 0
        shape_efficiency = 0.95 if impulse_type in ["lightning", "chopped"] else 0.85
        total_efficiency = circuit_efficiency * shape_efficiency
        log.debug(f"Eficiência: Total={total_efficiency:.3f}, Circuito={circuit_efficiency:.3f}, Forma={shape_efficiency:.2f}")
        return total_efficiency, circuit_efficiency, shape_efficiency, c_load
    except Exception as e: log.exception(f"Erro calc eficiência: {e}"); return 0, 0, 0, 0

def calculate_energy_requirements(actual_test_voltage_kv, c_load_f):
    try:
        if actual_test_voltage_kv is None or c_load_f is None: return 0.0
        test_voltage_v = float(actual_test_voltage_kv) * 1000; c_load_f = float(c_load_f)
        if c_load_f < 0: return 0.0
        energy_joules = 0.5 * c_load_f * (test_voltage_v**2); energy_kj = energy_joules / 1000
        log.debug(f"Energia carga @ {actual_test_voltage_kv:.1f} kV: {energy_kj:.2f} kJ")
        return energy_kj
    except (ValueError, TypeError) as e: log.error(f"Erro calc energia req: {e}"); return 0.0
    except Exception as e: log.error(f"Erro inesperado calc energia: {e}"); return 0.0

def _calculate_waveform_parameters(n_stages, n_parallel, rf_per_column, rt_per_column, c_test_object_pf, c_stray_pf, l_extra_h, transformer_inductance_h, inductor_value_h, impulse_type, generator_config_value, inductance_factor=1.0, tail_resistance_factor=1.0):
    log.debug(f"Calc params: {n_stages}S-{n_parallel}P, Tipo: {impulse_type}")
    c_gen_effective = calculate_effective_gen_params(n_stages, n_parallel)
    c_divider_f = get_divider_capacitance(generator_config_value)
    c_test_object_f = c_test_object_pf * 1e-12 if c_test_object_pf is not None else 0
    c_stray_f = c_stray_pf * 1e-12 if c_stray_pf is not None else 0
    c_load_extra_f = C_CHOPPING_GAP_PF * 1e-12 if impulse_type == "chopped" else 0
    c_load = c_test_object_f + c_divider_f + c_load_extra_f + c_stray_f
    c_eq = (c_gen_effective * c_load) / (c_gen_effective + c_load) if (c_gen_effective + c_load) > 1e-15 else 0
    rf_total_initial = (rf_per_column * n_stages) / n_parallel if n_parallel > 0 else float("inf")
    rt_total_initial = (rt_per_column * n_stages) / n_parallel if n_parallel > 0 else float("inf")
    l_gen = (L_PER_STAGE_H * n_stages) / n_parallel if n_parallel > 0 else 0
    l_total_initial = l_gen + l_extra_h + transformer_inductance_h + inductor_value_h
    log.debug(f"l_total_initial: l_gen={l_gen:.3e}, l_extra={l_extra_h:.3e}, l_trafo={transformer_inductance_h:.3e}, l_indutor={inductor_value_h:.3e}")
    rf_total = rf_total_initial
    l_total = l_total_initial * inductance_factor
    rt_total = rt_total_initial * tail_resistance_factor
    log.debug(f"RLC Efetivos (Ajustados): Rf={rf_total:.2f}, Rt={rt_total:.2f}, L={l_total:.3e}, Cg={c_gen_effective:.3e}, Cl={c_load:.3e}, Ceq={c_eq:.3e}")
    alpha = 1 / (rt_total * (c_gen_effective + c_load)) if rt_total > 1e-9 and (c_gen_effective + c_load) > 1e-15 else 0
    beta = 1 / (rf_total * c_eq) if rf_total > 1e-9 and c_eq > 1e-15 else 0
    if beta <= alpha + 1e-9: beta = alpha * 1.05 + 1e3; log.warning(f"Beta ({beta:.2e}) <= Alpha ({alpha:.2e}). Ajustando Beta para {beta:.2e}")
    r_eq_damping = rf_total + R_PARASITIC_OHM
    omega0 = 1 / math.sqrt(l_total * c_eq) if l_total * c_eq > 1e-18 else 0
    zeta = r_eq_damping / (2 * l_total * omega0) if l_total * omega0 > 1e-9 else float("inf")
    is_oscillatory = zeta < 1.0 if omega0 > 0 else False
    log.debug(f"Alpha={alpha:.2e}, Beta={beta:.2e}, Zeta={zeta:.3f}, Oscilatório={is_oscillatory}")
    return c_gen_effective, c_load, rf_total, rt_total, c_eq, alpha, beta, l_total, is_oscillatory, zeta

# --- Funções de Simulação da Forma de Onda ---
# (Mantidas como no original, mas com logging ajustado)
def rlc_solution(t_sec, v0, r_total, l_total, c_eq):
    if l_total <= 1e-12 or c_eq <= 1e-15 or r_total <= 1e-9: log.warning(f"RLC inválido: R={r_total}, L={l_total}, Ceq={c_eq}"); return np.zeros_like(t_sec)
    try:
        omega0_sq = 1.0 / (l_total * c_eq); alpha_damp = r_total / (2.0 * l_total)
        if alpha_damp <= 0: log.warning("Amortecimento RLC não positivo."); return np.zeros_like(t_sec)
        delta = alpha_damp**2 - omega0_sq
        if delta > 1e-12: # Superamortecido
            omega_d = math.sqrt(delta)
            s1 = -alpha_damp + omega_d; s2 = -alpha_damp - omega_d
            if abs(s1 - s2) < 1e-9: return v0 * (1 + s1 * t_sec) * np.exp(s1 * t_sec)
            A1 = v0 * s2 / (s2 - s1); A2 = -v0 * s1 / (s2 - s1)
            return A1 * np.exp(s1 * t_sec) + A2 * np.exp(s2 * t_sec)
        elif delta < -1e-12: # Subamortecido
            omega_d = math.sqrt(-delta)
            return v0 * (np.cos(omega_d * t_sec) + (alpha_damp / omega_d) * np.sin(omega_d * t_sec)) * np.exp(-alpha_damp * t_sec)
        else: # Criticamente amortecido
            return v0 * (1 + alpha_damp * t_sec) * np.exp(-alpha_damp * t_sec)
    except (ValueError, OverflowError) as e: log.error(f"Erro na solução RLC: {e}"); return np.zeros_like(t_sec)

def double_exponential(t_sec, v_peak, alpha, beta):
    if beta <= alpha + 1e-9: log.warning(f"Beta ({beta:.2e}) <= Alpha ({alpha:.2e}). Retornando zero."); return np.zeros_like(t_sec)
    try:
        norm_factor = 1.0 / (np.exp(-alpha * (np.log(beta / alpha) / (beta - alpha))) - np.exp(-beta * (np.log(beta / alpha) / (beta - alpha))))
        result = v_peak * norm_factor * (np.exp(-alpha * t_sec) - np.exp(-beta * t_sec))
        return np.maximum(0, result)
    except (ValueError, OverflowError, ZeroDivisionError) as e: log.error(f"Erro na dupla exponencial: {e}"); return np.zeros_like(t_sec)

def chopped_waveform(t_sec, v_peak, alpha, beta, chop_time_us):
    waveform = double_exponential(t_sec, v_peak, alpha, beta)
    chop_time_sec = chop_time_us * 1e-6
    chop_index = np.searchsorted(t_sec, chop_time_sec)
    if chop_index < len(t_sec):
        v_chop = waveform[chop_index]
        t_fall = 0.1e-6
        decay_rate = v_chop / t_fall
        time_after_chop = t_sec[chop_index:] - chop_time_sec
        waveform[chop_index:] = np.maximum(0, v_chop - decay_rate * time_after_chop)
    return waveform

# --- Funções de Análise da Forma de Onda ---
# (Mantidas como no original, mas com logging ajustado)
def find_waveform_parameters(t_us, v_norm, impulse_type, chop_time_us=None):
    if len(t_us) < 3 or len(v_norm) < 3 or np.max(v_norm) < 0.1: return {}, "Dados insuficientes"
    try:
        v_peak_actual = np.max(v_norm)
        t_peak_index = np.argmax(v_norm)
        t_peak_actual = t_us[t_peak_index]
        if v_peak_actual < 1e-6: return {}, "Tensão de pico muito baixa"
        indices_30 = np.where(v_norm >= 0.3 * v_peak_actual)[0]
        indices_90 = np.where(v_norm >= 0.9 * v_peak_actual)[0]
        if len(indices_30) == 0 or len(indices_90) == 0: return {}, "Não atingiu 30% ou 90%"
        t_30_start = t_us[indices_30[0]]; t_90_start = t_us[indices_90[0]]
        front_time = (t_90_start - t_30_start) / 0.6
        t_origin_est = t_30_start - 0.3 * front_time
        indices_50_tail = np.where((t_us > t_peak_actual) & (v_norm <= 0.5 * v_peak_actual))[0]
        if len(indices_50_tail) == 0: tail_time = float("inf"); log.warning("Não atingiu 50% na cauda.")
        else: t_50_tail = t_us[indices_50_tail[0]]; tail_time = t_50_tail - t_origin_est
        overshoot = ((v_peak_actual - 1.0) / 1.0) * 100 if v_peak_actual > 1.0 else 0
        params = {"front_time": front_time, "tail_time": tail_time, "peak_time": t_peak_actual, "peak_value": v_peak_actual, "overshoot": overshoot, "t_origin_est": t_origin_est}
        if impulse_type == "switching":
            indices_90_end = np.where((t_us > t_peak_actual) & (v_norm <= 0.9 * v_peak_actual))[0]
            t_90_end = t_us[indices_90_end[0]] if len(indices_90_end) > 0 else t_us[-1]
            params["time_above_90"] = t_90_end - t_90_start
            indices_zero_tail = np.where((t_us > t_peak_actual) & (v_norm <= 0.01 * v_peak_actual))[0]
            t_zero_tail = t_us[indices_zero_tail[0]] if len(indices_zero_tail) > 0 else t_us[-1]
            params["time_to_zero"] = t_zero_tail - t_origin_est
        if impulse_type == "chopped" and chop_time_us is not None:
            chop_time_index = np.searchsorted(t_us, chop_time_us)
            if chop_time_index > 0 and chop_time_index < len(v_norm):
                v_before_chop = v_norm[chop_time_index - 1]
                v_after_chop = v_norm[chop_time_index]
                undershoot_val = -np.min(v_norm[chop_time_index:]) if len(v_norm[chop_time_index:]) > 0 else 0
                params["undershoot"] = (undershoot_val / v_peak_actual) * 100 if v_peak_actual > 1e-6 else 0
            else: params["undershoot"] = 0
        return params, None
    except Exception as e: log.exception(f"Erro ao encontrar parâmetros da onda: {e}"); return {}, f"Erro: {e}"

def check_waveform_compliance(params, impulse_type, chop_time_us=None):
    if not params: return "REPROVADO", "Parâmetros não calculados"
    status = "APROVADO"
    details = []
    try:
        if impulse_type == "lightning":
            if not (LIGHTNING_IMPULSE_FRONT_TIME_NOM * (1 - LIGHTNING_FRONT_TOLERANCE) <= params["front_time"] <= LIGHTNING_IMPULSE_FRONT_TIME_NOM * (1 + LIGHTNING_FRONT_TOLERANCE)): status = "REPROVADO"; details.append(f"Tf={params["front_time"]:.2f}µs (Fora Tol)")
            if not (LIGHTNING_IMPULSE_TAIL_TIME_NOM * (1 - LIGHTNING_TAIL_TOLERANCE) <= params["tail_time"] <= LIGHTNING_IMPULSE_TAIL_TIME_NOM * (1 + LIGHTNING_TAIL_TOLERANCE)): status = "REPROVADO"; details.append(f"Tc={params["tail_time"]:.1f}µs (Fora Tol)")
            if not (1 - LIGHTNING_PEAK_TOLERANCE <= params["peak_value"] <= 1 + LIGHTNING_PEAK_TOLERANCE): status = "REPROVADO"; details.append(f"Vp={params["peak_value"]:.3f}pu (Fora Tol)")
            if params["overshoot"] > LIGHTNING_OVERSHOOT_MAX: status = "REPROVADO"; details.append(f"Overshoot={params["overshoot"]:.1f}% > {LIGHTNING_OVERSHOOT_MAX}%")
        elif impulse_type == "switching":
            if not (SWITCHING_IMPULSE_PEAK_TIME_NOM * (1 - SWITCHING_PEAK_TIME_TOLERANCE) <= params["peak_time"] <= SWITCHING_IMPULSE_PEAK_TIME_NOM * (1 + SWITCHING_PEAK_TIME_TOLERANCE)): status = "REPROVADO"; details.append(f"Tp={params["peak_time"]:.1f}µs (Fora Tol)")
            if not (SWITCHING_IMPULSE_TAIL_TIME_NOM * (1 - SWITCHING_TAIL_TOLERANCE) <= params["tail_time"] <= SWITCHING_IMPULSE_TAIL_TIME_NOM * (1 + SWITCHING_TAIL_TOLERANCE)): status = "REPROVADO"; details.append(f"T2={params["tail_time"]:.0f}µs (Fora Tol)")
            if not (1 - SWITCHING_PEAK_TOLERANCE <= params["peak_value"] <= 1 + SWITCHING_PEAK_TOLERANCE): status = "REPROVADO"; details.append(f"Vp={params["peak_value"]:.3f}pu (Fora Tol)")
            if params.get("time_above_90", 0) < SWITCHING_TIME_ABOVE_90_MIN: status = "REPROVADO"; details.append(f"T>90%={params.get("time_above_90", 0):.0f}µs < {SWITCHING_TIME_ABOVE_90_MIN}µs")
            # Time to zero check removed as per user feedback
        elif impulse_type == "chopped":
            if not (LIGHTNING_IMPULSE_FRONT_TIME_NOM * (1 - CHOPPED_FRONT_TOLERANCE) <= params["front_time"] <= LIGHTNING_IMPULSE_FRONT_TIME_NOM * (1 + CHOPPED_FRONT_TOLERANCE)): status = "REPROVADO"; details.append(f"Tf={params["front_time"]:.2f}µs (Fora Tol)")
            if not (1 - CHOPPED_PEAK_TOLERANCE <= params["peak_value"] <= 1 + CHOPPED_PEAK_TOLERANCE): status = "REPROVADO"; details.append(f"Vp={params["peak_value"]:.3f}pu (Fora Tol)")
            if chop_time_us is not None and not (CHOPPED_IMPULSE_CHOP_TIME_MIN <= chop_time_us <= CHOPPED_IMPULSE_CHOP_TIME_MAX): status = "REPROVADO"; details.append(f"Tchop={chop_time_us:.1f}µs (Fora Faixa {CHOPPED_IMPULSE_CHOP_TIME_MIN}-{CHOPPED_IMPULSE_CHOP_TIME_MAX}µs)")
            if params.get("undershoot", 0) > CHOPPED_UNDERSHOOT_MAX: status = "REPROVADO"; details.append(f"Undershoot={params.get("undershoot", 0):.1f}% > {CHOPPED_UNDERSHOOT_MAX}%")
        return status, ", ".join(details) if details else "Conforme"
    except KeyError as e: log.error(f"Chave faltando nos parâmetros: {e}"); return "ERRO", f"Parâmetro ausente: {e}"
    except Exception as e: log.exception(f"Erro na checagem de conformidade: {e}"); return "ERRO", f"Erro: {e}"

# --- Funções de Plotagem ---
# (Mantidas como no original)
def create_empty_impulse_figure(title="Forma de Onda de Impulso"):
    fig = go.Figure()
    fig.update_layout(
        title=title,
        xaxis_title="Tempo (µs)",
        yaxis_title="Tensão Normalizada (pu)",
        template="plotly_white",
        margin=dict(l=40, r=20, t=60, b=40),
        legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01),
    )
    return fig

def plot_impulse_waveform(t_us, v_norm, params, impulse_type, title="Forma de Onda de Impulso"):
    fig = create_empty_impulse_figure(title)
    fig.add_trace(go.Scatter(x=t_us, y=v_norm, mode="lines", name="Simulada"))
    if params and "t_origin_est" in params:
        t_origin = params["t_origin_est"]
        v_peak = params["peak_value"]
        t_peak = params["peak_time"]
        t_front = params["front_time"]
        t_tail = params["tail_time"]
        fig.add_vline(x=t_origin, line_dash="dash", line_color="grey", annotation_text="T0 (est)")
        fig.add_vline(x=t_peak, line_dash="dot", line_color="red", annotation_text=f"Tp={t_peak:.2f}µs")
        if impulse_type != "switching":
            t_50 = t_origin + t_tail
            fig.add_vline(x=t_50, line_dash="dashdot", line_color="green", annotation_text=f"T50%={t_50:.1f}µs")
            fig.add_hline(y=0.5 * v_peak, line_dash="dash", line_color="green")
        fig.add_hline(y=v_peak, line_dash="dot", line_color="red", annotation_text=f"Vp={v_peak:.3f}pu")
        if impulse_type != "switching":
            t_30 = t_origin + 0.3 * t_front
            t_90 = t_origin + 0.9 * t_front
            fig.add_vline(x=t_30, line_dash="dash", line_color="orange", annotation_text="T30%")
            fig.add_vline(x=t_90, line_dash="dash", line_color="orange", annotation_text="T90%")
            fig.add_hline(y=0.3 * v_peak, line_dash="dash", line_color="orange")
            fig.add_hline(y=0.9 * v_peak, line_dash="dash", line_color="orange")
    fig.update_layout(yaxis_range=[-0.1, max(1.1, np.max(v_norm) * 1.05)])
    return fig

# --- Callback Consolidado para Impulso ---
def register_impulse_callbacks(app_instance):
    log.info(f"Registrando callbacks CONSOLIDADOS do módulo impulse para app {app_instance.title}...")

    @app_instance.callback(
        [
            # Outputs da UI
            Output("impulse-waveform-graph", "figure"),
            Output("impulse-params-output", "children"),
            Output("impulse-compliance-output", "children"),
            Output("impulse-efficiency-output", "children"),
            Output("impulse-energy-output", "children"),
            Output("impulse-charging-voltage-output", "children"),
            Output("impulse-resistor-suggestion-output", "children"),
            Output("impulse-error-message", "children"),
            # Outputs para carregar dados na UI
            Output("impulse-type-selector", "value"),
            Output("generator-config-selector", "value"),
            Output("test-voltage-input", "value"),
            Output("rf-input", "value"),
            Output("rt-input", "value"),
            Output("c-test-object-input", "value"),
            Output("c-stray-input", "value"),
            Output("l-extra-input", "value"),
            Output("chop-time-input", "value"),
            Output("chop-time-input", "disabled"),
            Output("inductor-value-input", "value"),
            # Output do Store
            Output("impulse-store", "data", allow_duplicate=True),
            # Output do painel de info da página
            Output("transformer-info-impulse-page", "children"),
        ],
        [
            # Triggers principais
            Input("url", "pathname"),
            Input("transformer-inputs-store", "data"),
            Input("impulse-store", "data"),
            Input("simulate-impulse-btn", "n_clicks"),
            # Triggers secundários (mudança de inputs)
            Input("impulse-type-selector", "value"),
            Input("generator-config-selector", "value"),
            # Input do painel global para copiar para a página
            Input("transformer-info-impulse", "children"),
        ],
        [
            # States dos inputs para cálculo
            State("test-voltage-input", "value"),
            State("rf-input", "value"),
            State("rt-input", "value"),
            State("c-test-object-input", "value"),
            State("c-stray-input", "value"),
            State("l-extra-input", "value"),
            State("chop-time-input", "value"),
            State("inductor-value-input", "value"),
            # State do store local
            State("impulse-store", "data"),
        ],
        prevent_initial_call=False,
    )
    def consolidated_impulse_callback(
        pathname, transformer_data, stored_impulse_data, n_clicks,
        impulse_type_ui, generator_config_ui,
        global_panel_content,
        # States
        test_voltage_state, rf_state, rt_state, c_test_object_state, c_stray_state,
        l_extra_state, chop_time_state, inductor_value_state,
        current_store_data_state
    ):
        """
        Callback consolidado para o módulo de Impulso.
        Gerencia:
        - Carregamento de dados dos stores.
        - Simulação da forma de onda (acionado por botão ou stores).
        - Cálculo de parâmetros, eficiência, energia, tensão de carga.
        - Verificação de conformidade.
        - Sugestão de resistores.
        - Atualização da UI (gráficos, textos, inputs).
        - Salvamento dos resultados no store local.
        - Atualização do painel de informações na página.
        - Habilitar/desabilitar campo de tempo de corte.
        """
        triggered_id = ctx.triggered_id
        log.info(f"[CONSOLIDATED Impulse] Callback triggered by: {triggered_id}")

        # --- 0. Inicialização e Verificação de Contexto ---
        normalized_path = normalize_pathname(pathname)
        is_impulse_page = normalized_path == ROUTE_IMPULSE

        # Se não estiver na página e o trigger não for store ou botão, não faz nada na UI principal
        if not is_impulse_page and triggered_id not in [
            "transformer-inputs-store", "impulse-store", "simulate-impulse-btn",
            "impulse-type-selector", "generator-config-selector"
        ]:
            log.debug(f"[CONSOLIDATED Impulse] Não na página e trigger não relevante ({triggered_id}). Abortando atualização da UI principal.")
            # Retorna no_update para a maioria, mas atualiza o painel de info
            return (
                no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update,
                no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update, no_update,
                no_update, # store
                global_panel_content or "" # info panel
            )

        # --- 1. Atualizar Painel de Informações da Página ---
        page_info_panel_content = global_panel_content or ""

        # --- 2. Lógica de Carregamento de Dados ---
        # Determina os valores a serem usados (prioridade: UI > Store > Padrão/Global)

        # Valores do store local
        local_data = stored_impulse_data if stored_impulse_data and isinstance(stored_impulse_data, dict) else {}
        inputs_local = local_data.get("inputs_impulso", local_data)
        results_local = local_data.get("resultados_impulso", local_data)

        # Valores do store global (transformer-inputs)
        global_data = transformer_data if transformer_data and isinstance(transformer_data, dict) else {}
        transformer_dict = global_data.get("transformer_data", global_data)
        tensao_nominal_kv = safe_float(transformer_dict.get("tensao_at")) # Exemplo, ajustar conforme necessário
        potencia_mva = safe_float(transformer_dict.get("potencia_mva"))
        impedancia_percent = safe_float(transformer_dict.get("impedancia"))
        freq_hz = safe_float(transformer_dict.get("frequencia", 60))

        # Determinar valores finais para a UI e cálculo
        # Prioridade: Trigger de UI > State da UI > Store Local > Padrão/Global
        final_impulse_type = impulse_type_ui if triggered_id == "impulse-type-selector" else inputs_local.get("impulse_type", "lightning")
        final_generator_config = generator_config_ui if triggered_id == "generator-config-selector" else inputs_local.get("generator_config", "12S-1P")
        final_test_voltage = safe_float(test_voltage_state) if test_voltage_state is not None else safe_float(inputs_local.get("test_voltage_kv"))
        final_rf_expr = rf_state if rf_state is not None else inputs_local.get("rf_expression", "")
        final_rt_expr = rt_state if rt_state is not None else inputs_local.get("rt_expression", "")
        final_c_test_object = safe_float(c_test_object_state) if c_test_object_state is not None else safe_float(inputs_local.get("c_test_object_pf"))
        final_c_stray = safe_float(c_stray_state) if c_stray_state is not None else safe_float(inputs_local.get("c_stray_pf"))
        final_l_extra = safe_float(l_extra_state) if l_extra_state is not None else safe_float(inputs_local.get("l_extra_h"))
        final_chop_time = safe_float(chop_time_state) if chop_time_state is not None else safe_float(inputs_local.get("chop_time_us"))
        final_inductor_value = safe_float(inductor_value_state) if inductor_value_state is not None else safe_float(inputs_local.get("inductor_value_h"))

        # Habilitar/desabilitar chop_time
        chop_time_disabled = final_impulse_type != "chopped"
        if chop_time_disabled: final_chop_time = None # Limpa se desabilitado

        # Valores iniciais para outputs da UI (serão atualizados se houver cálculo)
        fig = create_empty_impulse_figure()
        params_text = "-"
        compliance_text = "-"
        efficiency_text = "-"
        energy_text = "-"
        charging_voltage_text = "-"
        resistor_suggestion_text = "-"
        error_message = ""

        # Carregar resultados do store se não for recalcular
        if triggered_id != "simulate-impulse-btn" and results_local:
            params = results_local.get("waveform_params", {})
            status = results_local.get("compliance_status", "-")
            details = results_local.get("compliance_details", "")
            eff_total = results_local.get("efficiency_total")
            eff_circuit = results_local.get("efficiency_circuit")
            eff_shape = results_local.get("efficiency_shape")
            energy_req = results_local.get("energy_required_kj")
            energy_gen = results_local.get("generator_energy_kj")
            v_charge = results_local.get("charging_voltage_kv")
            v_charge_max = results_local.get("max_charging_voltage_kv")
            rf_sugg = results_local.get("suggested_rf")
            rt_sugg = results_local.get("suggested_rt")

            # Atualizar textos da UI com dados do store
            if params: params_text = html.Pre(json.dumps(params, indent=2), style={
                "fontSize": "0.75rem", "maxHeight": "150px", "overflowY": "auto"
            })
            compliance_text = html.Span(f"{status} ({details})", style={
                "color": colors.get("pass", "green") if status == "APROVADO" else colors.get("fail", "red")
            })
            if eff_total is not None: efficiency_text = f"Total: {eff_total*100:.1f}% (Circuito: {eff_circuit*100:.1f}%, Forma: {eff_shape*100:.1f}%)"
            if energy_req is not None: energy_text = f"Carga: {energy_req:.2f} kJ | Gerador: {energy_gen:.1f} kJ ({("OK" if energy_gen >= energy_req else "INSUFICIENTE")})"
            if v_charge is not None: charging_voltage_text = f"Necessária: {v_charge:.1f} kV | Máx: {v_charge_max:.1f} kV ({("OK" if v_charge <= v_charge_max else "EXCEDIDA")})"
            if rf_sugg is not None: resistor_suggestion_text = f"Rf Sugerido: {rf_sugg:.1f} Ω | Rt Sugerido: {rt_sugg:.1f} Ω"

            # Tentar recriar figura se dados existirem
            t_us_stored = results_local.get("time_us")
            v_norm_stored = results_local.get("voltage_pu")
            if t_us_stored and v_norm_stored and params:
                 try: fig = plot_impulse_waveform(np.array(t_us_stored), np.array(v_norm_stored), params, final_impulse_type)
                 except Exception as e_fig: log.error(f"Erro ao recriar figura do store: {e_fig}")

        # --- 3. Lógica de Simulação e Cálculo (se botão foi clicado) ---
        store_data_to_save = no_update

        if triggered_id == "simulate-impulse-btn" and n_clicks:
            log.info(f"🚀 Botão Simular Impulso clicado ({n_clicks}x). Iniciando simulação...")
            error_message = ""
            params = {}
            status = "ERRO"
            details = "Falha na simulação"

            try:
                # Validar inputs numéricos
                inputs_valid = True
                if final_test_voltage is None: error_message += "Tensão de teste inválida. "; inputs_valid = False
                if final_c_test_object is None: error_message += "Capacitância obj. teste inválida. "; inputs_valid = False
                if final_c_stray is None: final_c_stray = 0 # Default
                if final_l_extra is None: final_l_extra = 0 # Default
                if final_inductor_value is None: final_inductor_value = 0 # Default
                if final_impulse_type == "chopped" and final_chop_time is None: error_message += "Tempo de corte inválido. "; inputs_valid = False

                # Parsear resistores
                rf_ohm, rf_special = parse_resistor_expression(final_rf_expr)
                rt_ohm, rt_special = parse_resistor_expression(final_rt_expr)
                if math.isinf(rf_ohm): error_message += f"Expressão Rf inválida ({final_rf_expr}). "; inputs_valid = False
                if math.isinf(rt_ohm): error_message += f"Expressão Rt inválida ({final_rt_expr}). "; inputs_valid = False

                if not inputs_valid:
                    raise ValueError(error_message.strip())

                # Obter parâmetros do gerador
                n_stages, n_parallel, max_voltage_kv_gen, energy_kj_gen = get_generator_params(final_generator_config)

                # Calcular indutância do transformador
                transformer_inductance_h = calculate_transformer_inductance(tensao_nominal_kv, potencia_mva, impedancia_percent, freq_hz)

                # Calcular eficiência e carga
                eff_total, eff_circuit, eff_shape, c_load_f = calculate_circuit_efficiency(
                    n_stages, n_parallel, final_c_test_object, final_c_stray, final_impulse_type, final_generator_config
                )
                efficiency_text = f"Total: {eff_total*100:.1f}% (Circuito: {eff_circuit*100:.1f}%, Forma: {eff_shape*100:.1f}%)"

                # Calcular energia
                energy_req_kj = calculate_energy_requirements(final_test_voltage, c_load_f)
                energy_status = "OK" if energy_kj_gen >= energy_req_kj else "INSUFICIENTE"
                energy_text = f"Carga: {energy_req_kj:.2f} kJ | Gerador: {energy_kj_gen:.1f} kJ ({energy_status})"

                # Calcular tensão de carga
                v_charge_kv = final_test_voltage / eff_total if eff_total > 1e-6 else float("inf")
                max_charge_volt_kv = max_voltage_kv_gen / n_stages
                charge_status = "OK" if v_charge_kv <= max_charge_volt_kv else "EXCEDIDA"
                charging_voltage_text = f"Necessária: {v_charge_kv:.1f} kV | Máx: {max_charge_volt_kv:.1f} kV ({charge_status})"

                # Calcular parâmetros da forma de onda
                c_gen_eff, _, rf_tot, rt_tot, c_eq, alpha, beta, l_tot, is_osc, zeta = _calculate_waveform_parameters(
                    n_stages, n_parallel, rf_ohm / n_stages, rt_ohm / n_stages, # Passa valor por coluna
                    final_c_test_object, final_c_stray, final_l_extra, transformer_inductance_h, final_inductor_value,
                    final_impulse_type, final_generator_config
                )

                # Simular forma de onda
                t_end_us = 200 if final_impulse_type != "switching" else 5000
                t_us = np.linspace(0, t_end_us, 2000)
                t_sec = t_us * 1e-6
                v0_charge = 1.0 # Tensão de carga normalizada para simulação

                if is_osc:
                    v_sim_norm = rlc_solution(t_sec, v0_charge, rf_tot + R_PARASITIC_OHM, l_tot, c_eq)
                else:
                    v_sim_norm = double_exponential(t_sec, v0_charge, alpha, beta)

                if final_impulse_type == "chopped":
                    v_sim_norm = chopped_waveform(t_sec, v0_charge, alpha, beta, final_chop_time)

                # Normalizar pelo pico para análise
                v_peak_sim = np.max(v_sim_norm)
                v_norm_analysis = v_sim_norm / v_peak_sim if v_peak_sim > 1e-6 else v_sim_norm

                # Analisar parâmetros e conformidade
                params, params_error = find_waveform_parameters(t_us, v_norm_analysis, final_impulse_type, final_chop_time)
                if params_error: error_message += f"Erro análise: {params_error}. "

                if params:
                    status, details = check_waveform_compliance(params, final_impulse_type, final_chop_time)
                    params_text = html.Pre(json.dumps(params, indent=2, default=lambda x: f"{x:.3f}"), style={
                        "fontSize": "0.75rem", "maxHeight": "150px", "overflowY": "auto", "whiteSpace": "pre-wrap"
                    })
                    compliance_text = html.Span(f"{status} ({details})", style={
                        "color": colors.get("pass", "green") if status == "APROVADO" else colors.get("fail", "red")
                    })
                    # Plotar gráfico
                    fig = plot_impulse_waveform(t_us, v_norm_analysis, params, final_impulse_type, f"Impulso {final_impulse_type.capitalize()} Simulado")
                else:
                    status = "ERRO"; details = "Falha na análise dos parâmetros"
                    compliance_text = html.Span(f"{status} ({details})", style={"color": colors.get("fail", "red")})
                    fig = create_empty_impulse_figure(f"Falha na Simulação - Impulso {final_impulse_type.capitalize()}")

                # Sugestão de resistores (simples, pode ser melhorada)
                rf_sugg = rf_ohm # Manter o calculado por enquanto
                rt_sugg = rt_ohm
                resistor_suggestion_text = f"Rf: {rf_sugg:.1f} Ω | Rt: {rt_sugg:.1f} Ω (Valores Calculados)"

                # Preparar dados para salvar no store
                inputs_to_save = {
                    "impulse_type": final_impulse_type,
                    "generator_config": final_generator_config,
                    "test_voltage_kv": final_test_voltage,
                    "rf_expression": final_rf_expr,
                    "rt_expression": final_rt_expr,
                    "c_test_object_pf": final_c_test_object,
                    "c_stray_pf": final_c_stray,
                    "l_extra_h": final_l_extra,
                    "chop_time_us": final_chop_time,
                    "inductor_value_h": final_inductor_value,
                }
                results_to_save = {
                    "waveform_params": params,
                    "compliance_status": status,
                    "compliance_details": details,
                    "efficiency_total": eff_total,
                    "efficiency_circuit": eff_circuit,
                    "efficiency_shape": eff_shape,
                    "energy_required_kj": energy_req_kj,
                    "generator_energy_kj": energy_kj_gen,
                    "charging_voltage_kv": v_charge_kv,
                    "max_charging_voltage_kv": max_charge_volt_kv,
                    "suggested_rf": rf_sugg,
                    "suggested_rt": rt_sugg,
                    "time_us": t_us.tolist() if params else [],
                    "voltage_pu": v_norm_analysis.tolist() if params else [],
                    "timestamp": datetime.datetime.now().isoformat()
                }
                store_data_to_save = {
                    "inputs_impulso": inputs_to_save,
                    "resultados_impulso": results_to_save
                }
                store_data_to_save = convert_numpy_types(store_data_to_save)
                log.info(f"[CONSOLIDATED Impulse] Preparando para salvar no store: {list(store_data_to_save["resultados_impulso"].keys())}")

            except ValueError as ve:
                log.warning(f"[CONSOLIDATED Impulse] Erro de validação: {ve}")
                error_message = html.Div(f"Erro de Validação: {ve}", style={"color": colors.get("fail", "red")})
                # Limpa resultados
                params_text, compliance_text, efficiency_text, energy_text, charging_voltage_text, resistor_suggestion_text = "-", "-", "-", "-", "-", "-"
                fig = create_empty_impulse_figure("Erro na Simulação")
                store_data_to_save = no_update
            except Exception as e:
                log.exception("[CONSOLIDATED Impulse] Erro inesperado durante a simulação")
                error_message = html.Div(f"Erro Inesperado: {e}", style={"color": colors.get("fail", "red")})
                # Limpa resultados
                params_text, compliance_text, efficiency_text, energy_text, charging_voltage_text, resistor_suggestion_text = "-", "-", "-", "-", "-", "-"
                fig = create_empty_impulse_figure("Erro na Simulação")
                store_data_to_save = no_update

        # --- 4. Retornar todos os outputs ---
        # Atualiza a UI com os valores finais determinados (carregados ou calculados)
        return (
            fig, params_text, compliance_text, efficiency_text, energy_text, charging_voltage_text, resistor_suggestion_text, error_message,
            # Valores para UI inputs (carregados ou mantidos)
            final_impulse_type, final_generator_config, final_test_voltage, final_rf_expr, final_rt_expr,
            final_c_test_object, final_c_stray, final_l_extra, final_chop_time, chop_time_disabled, final_inductor_value,
            # Store
            store_data_to_save,
            # Info panel
            page_info_panel_content,
        )

    log.info("Callbacks CONSOLIDADOS do módulo impulse registrados.")

# Remover ou comentar callbacks individuais antigos
# @app.callback(...) def update_impulse_page_info_panel(...)
# @app.callback(...) def impulse_load_data(...)
# @app.callback(...) def simulate_and_analyze_impulse(...)

