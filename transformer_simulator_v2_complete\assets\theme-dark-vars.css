/* assets/theme-dark-vars.css */
:root {
    --dark-primary: #26427A;
    --dark-secondary: #6c757d;
    --dark-accent: #00BFFF; /* Cyan */
    --dark-accent-alt: #FFD700; /* Gold */
    --dark-background-main: #1a1a1a;
    --dark-background-card: #2c2c2c;
    --dark-background-card-header: #1f1f1f;
    --dark-background-input: #3a3a3a;
    --dark-background-header: #1f1f1f;
    --dark-background-faint: #333333;
    --dark-text-light: #e0e0e0;
    --dark-text-dark: #e0e0e0; /* Corrigido: texto escuro no tema escuro deve ser claro */
    --dark-text-muted: #a0a0a0;
    --dark-text-header: #FFFFFF;
    --dark-border: #444444;
    --dark-border-light: #555555;
    --dark-border-strong: #666666;
    --dark-success: #28a745;
    --dark-danger: #dc3545;
    --dark-warning: #ffc107;
    --dark-info: #00BFFF; /* Cyan */
    --dark-pass: #28a745; /* Alias para success */
    --dark-fail: #dc3545; /* <PERSON>as para danger */
    --dark-pass-bg: rgba(40, 167, 69, 0.2);
    --dark-fail-bg: rgba(220, 53, 69, 0.2);
    --dark-warning-bg: rgba(255, 193, 7, 0.2);
    --dark-danger-bg-opaque: #5c1c1c; /* Opaque red for table status */
    --dark-warning-bg-faint-opaque: rgba(255, 193, 7, 0.3);
    --dark-warning-high-bg-faint-opaque: rgba(255, 165, 0, 0.3);
    --dark-ok-bg-faint-opaque: rgba(40, 167, 69, 0.3);
    --dark-info-bg-faint-opaque: rgba(0, 191, 255, 0.2);
    
    /* Adicionando variáveis para elementos específicos */
    --dark-input-text: #e0e0e0;
    --dark-input-placeholder: #888888;
    --dark-dropdown-bg: #2c2c2c;
    --dark-dropdown-text: #e0e0e0;
    --dark-dropdown-hover-bg: #3a3a3a;
    --dark-card-text: #e0e0e0;
    --dark-link-color: #00BFFF;
    --dark-link-hover: #99e6ff;
    --dark-table-header-bg: #1f1f1f;
    --dark-table-header-text: #FFFFFF;
    --dark-table-row-bg: #2c2c2c;
    --dark-table-row-alt-bg: #333333;
    --dark-table-border: #444444;
    --dark-code-bg: #2d2d2d;
    --dark-code-text: #f8f8f2;
    
    /* Adicionando variáveis para elementos específicos com problemas de contraste */
    --dark-button-text: #FFFFFF;
    --dark-button-bg: #26427A;
    --dark-button-hover-bg: #345694;
    --dark-button-active-bg: #1d325d;
    --dark-input-focus-border: #00BFFF;
    --dark-input-focus-shadow: rgba(0, 191, 255, 0.25);
    --dark-label-text: #e0e0e0;
    --dark-disabled-text: #a0a0a0;
    --dark-disabled-bg: #2a2a2a;
}
