"""
Utilitário para registrar alterações de inputs de forma mais detalhada.
Este módulo fornece funções para registrar alterações específicas nos inputs
e ajudar na depuração de problemas relacionados a alterações de dados.
"""
import logging
from typing import Dict, Any, Optional, List

# Obter logger
log = logging.getLogger(__name__)

def log_input_change(
    field_name: str,
    new_value: Any,
    old_value: Optional[Any] = None,
    module_name: Optional[str] = None,  # Mantido para compatibilidade
    store_id: Optional[str] = None      # Mantido para compatibilidade
) -> None:
    """
    Registra uma alteração específica em um campo de input.

    Args:
        field_name: Nome do campo alterado
        new_value: Novo valor do campo
        old_value: Valor anterior do campo (opcional)
        module_name: Nome do módulo onde ocorreu a alteração (opcional)
        store_id: ID do store onde o campo está armazenado (opcional)
    """
    # Ignorar alterações para o mesmo valor
    if old_value == new_value:
        return

    # Formatar valores para exibição
    def format_value(val):
        if isinstance(val, (int, float)) and val is not None:
            return f"{val}"
        elif isinstance(val, str):
            return f"'{val}'"
        else:
            return str(val)

    # Formatar mensagem
    formatted_new_value = format_value(new_value)

    if old_value is not None and old_value != new_value:
        formatted_old_value = format_value(old_value)
        # Mensagem simplificada com valor anterior e novo valor
        log.info(f"[INPUT CHANGE] Campo '{field_name}': {formatted_old_value} → {formatted_new_value}")

def log_dict_changes(
    new_data: Dict[str, Any],
    old_data: Dict[str, Any],
    module_name: Optional[str] = None,
    store_id: Optional[str] = None,
    ignore_fields: Optional[List[str]] = None
) -> None:
    """
    Registra alterações entre dois dicionários de dados.

    Args:
        new_data: Dicionário com os novos dados
        old_data: Dicionário com os dados antigos
        module_name: Nome do módulo onde ocorreu a alteração (opcional)
        store_id: ID do store onde os dados estão armazenados (opcional)
        ignore_fields: Lista de campos a serem ignorados (opcional)
    """
    if ignore_fields is None:
        ignore_fields = [
            # Campos internos e metadados
            "_timestamp", "metadata", "_load_error", "transformer_data",
            # Campos calculados automaticamente
            "corrente_nominal_at", "corrente_nominal_bt", "corrente_nominal_terciario",
            "corrente_nominal_at_tap_maior", "corrente_nominal_at_tap_menor"
        ]

    # Campos importantes que sempre devem ser registrados
    # APENAS estes campos serão registrados, todos os outros serão ignorados
    important_fields = [
        "potencia_mva", "tensao_at", "tensao_bt", "tensao_terciario",
        "impedancia", "frequencia", "tipo_transformador", "grupo_ligacao"
    ]

    # Verificar APENAS os campos importantes
    for key in important_fields:
        # Obter valores
        new_value = new_data.get(key)
        old_value = old_data.get(key)

        # Verificar se o valor foi alterado e não é None
        if old_value != new_value and (new_value is not None or old_value is not None):
            log_input_change(key, new_value, old_value, module_name, store_id)

    # Verificar campos removidos (apenas campos importantes)
    for key in old_data:
        if key in important_fields and key not in new_data and key not in ignore_fields:
            log.info(f"[INPUT CHANGE] [{module_name if module_name else 'Unknown'}] Campo '{key}' removido")

def enable_detailed_logging(enable: bool = True) -> None:
    """
    Ativa ou desativa o registro detalhado de alterações.

    Args:
        enable: Se True, ativa o registro detalhado; se False, desativa
    """
    # Obter o logger raiz
    root_logger = logging.getLogger()

    # Ajustar nível de log para todos os handlers
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler):
            if enable:
                # No modo detalhado, mostrar todos os logs
                for f in handler.filters:
                    handler.removeFilter(f)
                handler.setLevel(logging.DEBUG)

                # Também ajustar o nível dos loggers específicos
                logging.getLogger('werkzeug').setLevel(logging.INFO)
                logging.getLogger('dash').setLevel(logging.INFO)
                logging.getLogger('utils').setLevel(logging.DEBUG)
                logging.getLogger('app_core').setLevel(logging.DEBUG)
                logging.getLogger('callbacks').setLevel(logging.DEBUG)

                # Ajustar o nível do logger raiz
                root_logger.setLevel(logging.DEBUG)

                log.warning("[INPUT CHANGE] Registro DETALHADO ativado - mostrando TODOS os logs")
            else:
                # No modo simplificado, readicionar o filtro e definir níveis altos
                try:
                    # Tentar importar o filtro do módulo app
                    from app import InputChangeFilter
                    handler.addFilter(InputChangeFilter())
                except (ImportError, AttributeError):
                    # Se não conseguir importar, apenas definir o nível
                    pass

                # Definir nível alto para o handler
                handler.setLevel(logging.WARNING)

                # Também ajustar o nível dos loggers específicos
                logging.getLogger('werkzeug').setLevel(logging.ERROR)
                logging.getLogger('dash').setLevel(logging.ERROR)
                logging.getLogger('utils').setLevel(logging.WARNING)
                logging.getLogger('app_core').setLevel(logging.WARNING)
                logging.getLogger('callbacks').setLevel(logging.WARNING)

                # Ajustar o nível do logger raiz
                root_logger.setLevel(logging.WARNING)

                log.warning("[INPUT CHANGE] Registro SIMPLIFICADO ativado - mostrando apenas alterações importantes")
