# app.py
import logging
import os
import sys
import webbrowser

import dash
import dash_bootstrap_components as dbc

# --- 1. Load Configuration FIRST ---
try:
    import config
except ImportError:
    logging.basicConfig(
        level=logging.CRITICAL,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    logging.critical("Não foi possível importar config.py. Usando configuração de fallback.")

    class ConfigFallback:
        LOG_DIR = "."
        LOG_FILE = "app_fallback.log"
        LOGGING_LEVEL = logging.INFO
        LOGGING_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
        LOGGING_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
        APP_TITLE = "Simulador (Erro Config)"
        DEFAULT_THEME_URL = dbc.themes.DARKLY
        DEFAULT_THEME_NAME = "DARKLY"
        ASSETS_DIR = "assets"
        USAGE_COUNT_FILE = "usage_count.txt"
        USAGE_LIMIT = 61
        HOST = "127.0.0.1"
        PORT = 8060
        DEBUG_MODE = False

    config = ConfigFallback()

# --- 2. Configure Logging SECOND ---
try:
    root_logger = logging.getLogger()
    if root_logger.handlers and any(isinstance(h, logging.FileHandler) for h in root_logger.handlers):
        log = logging.getLogger(__name__)
        log.debug("Logging já configurado anteriormente. Reutilizando configuração.")
    else:
        os.makedirs(config.LOG_DIR, exist_ok=True)

        global InputChangeFilter

        class InputChangeFilter(logging.Filter):
            def filter(self, record):
                if record.levelno >= logging.WARNING:
                    return True
                if "[INPUT CHANGE]" in record.getMessage():
                    return True
                return False

        log_file_path = os.path.join(config.LOG_DIR, config.LOG_FILE)

        file_handler = logging.FileHandler(log_file_path, mode="a", encoding="utf-8")
        file_handler.setLevel(logging.WARNING)
        file_handler.setFormatter(logging.Formatter(config.LOGGING_FORMAT, datefmt=config.LOGGING_DATE_FORMAT))

        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.WARNING)
        console_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s", datefmt="%H:%M:%S"))
        console_handler.addFilter(InputChangeFilter())

        root_logger = logging.getLogger()
        root_logger.setLevel(logging.WARNING)

        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        logging.getLogger('werkzeug').setLevel(logging.ERROR)
        logging.getLogger('dash').setLevel(logging.ERROR)
        logging.getLogger('utils').setLevel(logging.ERROR)
        logging.getLogger('app_core').setLevel(logging.ERROR)
        logging.getLogger('callbacks').setLevel(logging.ERROR)
        logging.getLogger('callbacks.insulation_level_callbacks').setLevel(logging.ERROR)
        logging.getLogger('app_core.isolation_repo').setLevel(logging.ERROR)

        log = logging.getLogger(__name__)
        log.info(f"Logging configurado. Nível: {logging.getLevelName(config.LOGGING_LEVEL)}. Arquivo: {log_file_path}")
except Exception as e:
    print(f"ERRO ao configurar logging: {e}")
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    log = logging.getLogger(__name__)
    log.error(f"Erro ao configurar logging: {e}")


# --- 3. Usage Limit Check Function ---
def verificar_e_incrementar_uso(incrementar=True):
    try:
        from utils.paths import get_data_dir
        from utils.usage_tracker import UsageTracker

        db_path = get_data_dir() / "usage.db"
        tracker = UsageTracker(str(db_path))

        if incrementar:
            uso_atual, limite_atingido = tracker.increment_counter("app_usage")
            log.info(f"Contador incrementado para: {uso_atual}")
        else:
            uso_atual = tracker.get_counter("app_usage") or 0
            limite_atingido = uso_atual >= config.USAGE_LIMIT
            log.info(f"Contador lido sem incremento: {uso_atual}")

        log.info(f"Uso atual: {uso_atual}, Limite: {config.USAGE_LIMIT}, Atingido: {limite_atingido}")
        return limite_atingido, uso_atual
    except Exception as e:
        log.error(f"Erro ao verificar/incrementar uso: {e}", exc_info=True)
        return False, 0


# --- 4. Initialize Dash App ---
log.info(f"Inicializando a aplicação Dash: {config.APP_TITLE}")
app = dash.Dash(
    __name__,
    external_stylesheets=[config.DEFAULT_THEME_URL],
    suppress_callback_exceptions=True,
    prevent_initial_callbacks=True,
    meta_tags=[
        {"name": "viewport", "content": "width=device-width, initial-scale=1.0"},
        {"name": "description", "content": "Simulador de Testes de Transformadores"},
        {"name": "theme-color", "content": "#4F4F4F"},
    ],
    title=config.APP_TITLE,
    assets_folder=config.ASSETS_DIR,
)
server = app.server
log.info(f"Tema Bootstrap: {config.DEFAULT_THEME_NAME}")
log.info(f"Pasta assets: {config.ASSETS_DIR}")

# --- 5. Initialize State Manager ---
try:
    from app_core.state_manager import state_manager
    app.state_manager = state_manager
    log.info("StateManager (app.state_manager) inicializado com sucesso.")
except Exception as e:
    log.critical(f"FALHA CRÍTICA ao inicializar StateManager: {e}", exc_info=True)
    app.state_manager = None

# --- 6. Perform Usage Limit Check ---
deve_incrementar = not config.DEBUG_MODE or os.environ.get("WERKZEUG_RUN_MAIN") != "true"
limite_atingido_inicial, uso_atual = verificar_e_incrementar_uso(incrementar=deve_incrementar)

log.info(f"Status final após verificação/incremento: Uso={uso_atual}, Limite={config.USAGE_LIMIT}, Atingido={limite_atingido_inicial}")
if limite_atingido_inicial:
    log.warning(f"Limite ({config.USAGE_LIMIT}) atingido ou excedido. Uso: {uso_atual}. Funcionalidades podem estar restritas.")

# --- 7. Create Main Application Layout ---
layout_creation_failed = False
error_layout = dbc.Container([dbc.Alert("Erro Crítico na Inicialização", color="danger", className="m-5")])
try:
    log.info("Importando e criando layout principal...")
    from layouts.main_layout import create_main_layout

    app.layout = create_main_layout(
        uso_atual=uso_atual,
        limite_atingido=limite_atingido_inicial,
        app=app,
    )
    log.info("Layout principal da aplicação definido e atribuído a app.layout.")
except Exception as e:
    log.critical("ERRO CRÍTICO AO CRIAR LAYOUT", exc_info=True)
    log.critical(f"Erro inesperado durante a criação do layout: {e}", exc_info=True)
    app.layout = error_layout
    layout_creation_failed = True

# --- 8. Import and Register Callbacks ---
if not layout_creation_failed:
    log.info("Importando e registrando callbacks (APÓS app.layout)...")
    try:
        decorated_modules = [
            "navigation_dcc_links",
            "losses",
            "dieletric_analysis",
            "report_generation",
            "dielectric_analysis_comprehensive",
            "global_updates",
            "standards_consultation",
            "standards_management",
            "logging_controls",
        ]
        for module_name in decorated_modules:
            try:
                module_path = f"callbacks.{module_name}"
                log.debug(f"Importando módulo de callback decorado: {module_path}")
                __import__(module_path)
                log.debug(f"Módulo importado: {module_path}")
            except ImportError as e:
                log.error(f"Erro ao importar módulo de callback {module_name}: {e}", exc_info=True)
            except Exception as e:
                log.error(f"Erro inesperado ao processar módulo de callback {module_name}: {e}", exc_info=True)

        explicit_registrations = {
            "insulation_level_callbacks": "register_insulation_level_callbacks",
            "transformer_inputs": "register_transformer_inputs_callbacks",
            "short_circuit": "register_short_circuit_callbacks",
            "impulse": "register_impulse_callbacks",
            "applied_voltage": "register_applied_voltage_callbacks",
            "induced_voltage": "register_induced_voltage_callbacks",
            "history": "register_history_callbacks",
            "global_actions": "register_global_actions_callbacks",
            "temperature_rise": "register_temperature_rise_callbacks"
        }

        for module_name, reg_func_name in explicit_registrations.items():
            try:
                module_path = f"callbacks.{module_name}"
                log.debug(f"Importando e registrando explicitamente: {module_path}")
                module = __import__(module_path, fromlist=[reg_func_name])
                registration_function = getattr(module, reg_func_name)
                registration_function(app)
                log.debug(f"Callbacks registrados explicitamente: {module_path}")
            except ImportError as e:
                log.error(f"Erro ao importar módulo para registro explícito {module_name}: {e}", exc_info=True)
            except AttributeError as e:
                log.error(f"Função de registro '{reg_func_name}' não encontrada em {module_name}: {e}", exc_info=True)
            except Exception as e:
                log.error(f"Erro ao registrar callbacks de {module_name} explicitamente: {e}", exc_info=True)

        try:
            from utils.standards_db import create_standards_tables
            create_standards_tables()
            log.debug("Banco de dados de normas técnicas inicializado/verificado.")
        except Exception as e:
            log.error(f"Erro ao inicializar banco de dados de normas: {e}", exc_info=True)

        log.info(f"Callbacks registrados. Total: {len(app.callback_map)}")
    except Exception as e:
        log.critical("ERRO CRÍTICO AO REGISTRAR CALLBACKS", exc_info=True)
        log.critical(f"Erro inesperado durante o registro de callbacks: {e}", exc_info=True)
        layout_creation_failed = True

# --- 9. Browser Opening Logic ---
_browser_opened = False


def open_browser():
    host = getattr(config, "HOST", "127.0.0.1")
    port = getattr(config, "PORT", 8050)
    url = f"http://{host}:{port}/"
    log.info(f"Abrindo navegador na URL: {url}")
    try:
        webbrowser.open_new(url)
    except Exception as e:
        log.error(f"Falha ao abrir o navegador: {e}")


# --- 10. Run Server ---
if __name__ == "__main__":
    if layout_creation_failed:
        log.error("Início do servidor abortado devido a falha na criação do layout ou registro de callbacks.")
    else:
        if limite_atingido_inicial:
            log.warning("LIMITE DE USO ATINGIDO - Funcionalidades podem estar restritas")

        host = getattr(config, "HOST", "127.0.0.1")
        port = getattr(config, "PORT", 8050)
        debug_mode = getattr(config, "DEBUG_MODE", True)

        log.info(f"Iniciando servidor Dash em http://{host}:{port}/ (Debug: {debug_mode})")

        if not debug_mode or os.environ.get("WERKZEUG_RUN_MAIN") == "true":
            if not _browser_opened:
                import threading
                threading.Timer(1.5, open_browser).start()
                _browser_opened = True
                log.info("Timer para abertura do navegador iniciado")
        else:
            log.debug("Não abrindo navegador (processo principal do reloader)")

        try:
            import atexit

            def save_mcp_on_exit():
                if hasattr(app, "state_manager") and app.state_manager is not None:
                    log.info("Saving StateManager state to disk before exit...")
                    try:
                        app.state_manager.save(force=True)
                        log.info("StateManager state saved successfully on exit")
                    except Exception as e:
                        log.error(f"Error saving StateManager state on exit: {e}", exc_info=True)

            atexit.register(save_mcp_on_exit)

            app.run(
                debug=False,
                host=host,
                port=port,
                use_reloader=False,
                threaded=True,
            )
            log.info("Servidor Dash encerrado.")
        except OSError as e:
            log.error(f"Erro ao iniciar o servidor na porta {port}: {e}")
            log.critical(f"ERRO FATAL: Não foi possível iniciar o servidor na porta {port}. Verifique se ela já está em uso.")
        except Exception as e:
            log.exception(f"Erro inesperado ao iniciar o servidor: {e}")
