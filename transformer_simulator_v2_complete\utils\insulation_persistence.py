# utils/insulation_persistence.py
"""
Utilitário para garantir a persistência dos valores de isolamento.
Este módulo fornece funções para salvar, validar e recuperar valores de isolamento
de forma consistente, evitando problemas de perda de dados entre navegações.
"""

import logging
from typing import Dict, Any, Optional, List, Union

# Obter logger
log = logging.getLogger(__name__)

def save_insulation_value(app, field_name: str, value: Any, store_id: str = "transformer-inputs-store") -> None:
    """
    Salva um valor de isolamento no state_manager ou MCP, garantindo que seja propagado corretamente.

    Args:
        app: Instância da aplicação Dash com acesso ao state_manager ou MCP
        field_name: Nome do campo de isolamento (ex: nbi_at, sil_bt, etc.)
        value: Valor a ser salvo
        store_id: ID do store onde o campo está armazenado (padrão: transformer-inputs-store)
    """
    if hasattr(app, 'state_manager') and app.state_manager is not None:
        # Usar state_manager se disponível
        pass
    elif hasattr(app, 'mcp') and app.mcp is not None:
        # Usar MCP como fallback
        pass
    else:
        log.warning(f"[INSULATION PERSISTENCE] App não possui state_manager nem MCP. Não foi possível salvar {field_name}={value}")
        return

    # Criar dicionário com o campo a ser atualizado
    update_data = {field_name: value}

    # Registrar a alteração
    log.warning(f"[INSULATION PERSISTENCE] Salvando {field_name}={value} no store {store_id}")

    # Salvar no state_manager ou MCP com propagação automática
    try:
        if hasattr(app, 'state_manager') and app.state_manager is not None:
            app.state_manager.set_store(store_id, update_data)
            log.warning(f"[INSULATION PERSISTENCE] Valor salvo com sucesso no state_manager: {field_name}={value}")
        elif hasattr(app, 'mcp') and app.mcp is not None:
            app.mcp.set_data(store_id, update_data, auto_propagate=True)
            log.warning(f"[INSULATION PERSISTENCE] Valor salvo com sucesso no MCP: {field_name}={value}")
        else:
            log.error(f"[INSULATION PERSISTENCE] Nem state_manager nem MCP estão disponíveis. Não foi possível salvar {field_name}={value}")
    except Exception as e:
        log.error(f"[INSULATION PERSISTENCE] Erro ao salvar {field_name}={value}: {e}")

def validate_insulation_options(um_kv: float, standard: str, field_type: str) -> List[str]:
    """
    Valida as opções de isolamento com base na classe de tensão e na norma.

    Args:
        um_kv: Classe de tensão (Um) em kV
        standard: Norma (IEC, IEEE, NBR)
        field_type: Tipo de campo (nbi, sil, ta, ti)

    Returns:
        Lista de valores válidos para o campo
    """
    # Valores padrão para diferentes classes de tensão e normas
    # Baseado na tabela.json
    options_map = {
        "IEC": {
            # Um: {field_type: [valores]}
            145: {
                "nbi": ["450", "550", "650"],
                "sil": ["NA_SIL"],
                "ta": ["185", "230", "275"],
                "ti": ["230", "275"]
            },
            245: {
                "nbi": ["750", "850", "950", "1050"],
                "sil": ["650", "750", "850"],
                "ta": ["325", "360", "395", "460"],
                "ti": ["360", "395", "460"]
            },
            52: {
                "nbi": ["250"],
                "sil": ["NA_SIL"],
                "ta": ["95"],
                "ti": []
            }
        },
        "IEEE": {
            # Um: {field_type: [valores]}
            161: {
                "nbi": ["550", "650", "750"],
                "sil": ["NA_SIL"],
                "ta": ["230", "275", "325"],
                "ti": ["170"]
            },
            230: {
                "nbi": ["650", "750", "825", "900"],
                "sil": ["NA_SIL"],
                "ta": ["275", "325", "360", "395"],
                "ti": ["240"]
            }
        }
    }

    # Mapear norma para formato padronizado
    std_key = "IEC" if standard in ["IEC", "NBR"] else "IEEE"

    # Tentar obter valores para a classe de tensão e norma específicas
    if std_key in options_map and um_kv in options_map[std_key]:
        return options_map[std_key][um_kv].get(field_type, [])

    # Se não encontrar valores específicos, retornar lista vazia
    log.debug(f"[INSULATION PERSISTENCE] Sem opções específicas para Um={um_kv}, Norma={standard}, Campo={field_type}")
    return []

def recover_insulation_state(app, field_name: str, store_id: str = "transformer-inputs-store") -> Optional[Any]:
    """
    Recupera o estado de um campo de isolamento do armazenamento persistente.

    Args:
        app: Instância da aplicação Dash com acesso ao state_manager ou MCP
        field_name: Nome do campo de isolamento (ex: nbi_at, sil_bt, etc.)
        store_id: ID do store onde o campo está armazenado (padrão: transformer-inputs-store)

    Returns:
        Valor armazenado ou None se não existir
    """
    if hasattr(app, 'state_manager') and app.state_manager is not None:
        # Usar state_manager se disponível
        pass
    elif hasattr(app, 'mcp') and app.mcp is not None:
        # Usar MCP como fallback
        pass
    else:
        log.warning(f"[INSULATION PERSISTENCE] App não possui state_manager nem MCP. Não foi possível recuperar {field_name}")
        return None

    try:
        # Obter dados do state_manager ou MCP
        if hasattr(app, 'state_manager') and app.state_manager is not None:
            stored_data = app.state_manager.get_store(store_id)
        elif hasattr(app, 'mcp') and app.mcp is not None:
            stored_data = app.mcp.get_data(store_id)
        else:
            stored_data = None

        # Verificar se os dados existem e se o campo está presente
        if stored_data and field_name in stored_data:
            value = stored_data[field_name]
            log.warning(f"[INSULATION PERSISTENCE] Valor recuperado: {field_name}={value}")
            return value

        log.warning(f"[INSULATION PERSISTENCE] Campo {field_name} não encontrado no store {store_id}")
        return None
    except Exception as e:
        log.error(f"[INSULATION PERSISTENCE] Erro ao recuperar {field_name}: {e}")
        return None

def ensure_insulation_value_in_options(value: Any, options: List[Dict[str, str]]) -> bool:
    """
    Verifica se um valor está na lista de opções e o adiciona se necessário.

    Args:
        value: Valor a ser verificado
        options: Lista de opções no formato [{"label": "...", "value": "..."}]

    Returns:
        True se o valor foi adicionado, False se já existia ou não foi possível adicionar
    """
    if value is None or not options:
        return False

    # Converter valor para string para comparação
    str_value = str(value)

    # Verificar se o valor já está nas opções
    if any(opt["value"] == str_value for opt in options):
        return False

    # Adicionar o valor às opções
    try:
        # Determinar o tipo de campo com base no valor
        if str_value == "NA_SIL":
            options.insert(0, {"label": "Não Aplicável", "value": "NA_SIL"})
        else:
            # Tentar determinar o tipo de campo com base no valor
            if float(str_value) > 1000:  # Provavelmente NBI
                options.append({"label": f"{str_value} kVp (Salvo)", "value": str_value})
            elif float(str_value) > 100:  # Provavelmente NBI ou SIL
                options.append({"label": f"{str_value} kVp (Salvo)", "value": str_value})
            else:  # Provavelmente TA ou TI
                options.append({"label": f"{str_value} kVrms (Salvo)", "value": str_value})

        log.warning(f"[INSULATION PERSISTENCE] Valor {str_value} adicionado às opções")
        return True
    except (ValueError, TypeError):
        # Se não for possível converter para float, adicionar como string genérica
        options.append({"label": f"{str_value} (Salvo)", "value": str_value})
        log.warning(f"[INSULATION PERSISTENCE] Valor {str_value} adicionado às opções como string")
        return True
