<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON>ilo<PERSON> - Documentação</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary-color: #0078D7;
            --secondary-color: #B9D1EA;
            --text-color: #f8f9fa;
            --bg-color: #343a40;
            --card-bg-color: #495057;
            --border-color: #6c757d;
        }
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 20px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .content {
            background-color: var(--card-bg-color);
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: var(--secondary-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        a {
            color: var(--primary-color);
            text-decoration: none;
        }
        a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }
        pre {
            background-color: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        code {
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e6e6e6;
        }
        table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid var(--border-color);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: var(--primary-color);
            color: white;
        }
        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .toc {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .toc a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .toc .toc-h2 {
            padding-left: 20px;
        }
        .toc .toc-h3 {
            padding-left: 40px;
        }
        .toc .toc-h4 {
            padding-left: 60px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #search-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        .highlight {
            background-color: rgba(255, 255, 0, 0.2);
            padding: 2px;
            border-radius: 2px;
        }
        .home-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <a href="index.html" class="home-link">← Voltar para Índice</a>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Buscar na documentação...">
                    </div>
                    <h5>Sumário</h5>
                    <ul class="toc" id="toc"></ul>
                </div>
            </div>
            <div class="col-md-9">
                <div class="content">
                    <div id="markdown-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Markdown content
        const markdownContent = `# Guia de Estilos

Este documento descreve o sistema de design da aplicação, incluindo cores, tipografia, espaçamento e componentes.

## Cores

### Tema Escuro

| Nome | Valor | Uso |
|------|-------|-----|
| \`--dark-primary\` | #26427A | Cor principal, usada em cabeçalhos, botões primários |
| \`--dark-secondary\` | #6c757d | Cor secundária, usada em botões secundários |
| \`--dark-accent\` | #007BFF | Cor de destaque, usada em links e elementos interativos |
| \`--dark-background-main\` | #4F4F4F | Fundo principal da aplicação |
| \`--dark-background-card\` | #595959 | Fundo de cards e painéis |
| \`--dark-background-card-header\` | #3A3A3A | Fundo de cabeçalhos de cards |
| \`--dark-background-input\` | #3A3A3A | Fundo de campos de entrada |
| \`--dark-text-light\` | #FFFFFF | Texto claro para fundos escuros |
| \`--dark-text-dark\` | #212529 | Texto escuro para fundos claros |
| \`--dark-text-muted\` | #CCCCCC | Texto atenuado |
| \`--dark-text-header\` | #FFFFFF | Texto para cabeçalhos |
| \`--dark-border\` | #6E6E6E | Cor de borda |
| \`--dark-success\` | #198754 | Cor de sucesso |
| \`--dark-danger\` | #dc3545 | Cor de erro |
| \`--dark-warning\` | #ffc107 | Cor de aviso |
| \`--dark-info\` | #0dcaf0 | Cor de informação |

### Tema Claro

| Nome | Valor | Uso |
|------|-------|-----|
| \`--light-primary\` | #26427A | Cor principal, usada em cabeçalhos, botões primários |
| \`--light-secondary\` | #6c757d | Cor secundária, usada em botões secundários |
| \`--light-accent\` | #007BFF | Cor de destaque, usada em links e elementos interativos |
| \`--light-background-main\` | #0078D7 | Fundo principal da aplicação |
| \`--light-background-card\` | #B9D1EA | Fundo de cards e painéis |
| \`--light-background-card-header\` | #26427A | Fundo de cabeçalhos de cards |
| \`--light-background-input\` | #FFFFFF | Fundo de campos de entrada |
| \`--light-text-dark\` | #000000 | Texto escuro para fundos claros |
| \`--light-text-header\` | #FFFFFF | Texto para cabeçalhos |
| \`--light-text-muted\` | #444444 | Texto atenuado |
| \`--light-text-on-primary\` | #FFFFFF | Texto sobre cor primária |
| \`--light-border\` | #26427A | Cor de borda |
| \`--light-success\` | #198754 | Cor de sucesso |
| \`--light-danger\` | #dc3545 | Cor de erro |
| \`--light-warning\` | #ffc107 | Cor de aviso |
| \`--light-info\` | #0dcaf0 | Cor de informação |

## Tipografia

### Tamanhos de Fonte

| Elemento | Tamanho | Peso | Uso |
|----------|---------|------|-----|
| Título Principal | 1.1rem | Bold | Título da aplicação |
| Título de Seção | 0.9rem | Bold | Cabeçalhos de cards e seções |
| Subtítulo | 0.85rem | Bold | Subtítulos e cabeçalhos secundários |
| Texto Normal | 0.75rem | Normal | Texto principal, labels, valores |
| Texto Pequeno | 0.7rem | Normal | Texto secundário, notas, legendas |

### Classes CSS para Tipografia

| Classe | Propriedades | Uso |
|--------|--------------|-----|
| \`.text-xs\` | \`font-size: 0.7rem\` | Texto muito pequeno |
| \`.text-sm\` | \`font-size: 0.75rem\` | Texto pequeno |
| \`.text-md\` | \`font-size: 0.85rem\` | Texto médio |
| \`.text-lg\` | \`font-size: 0.9rem\` | Texto grande |
| \`.text-xl\` | \`font-size: 1rem\` | Texto muito grande |
| \`.text-2xl\` | \`font-size: 1.1rem\` | Texto extra grande |
| \`.font-normal\` | \`font-weight: normal\` | Peso normal |
| \`.font-medium\` | \`font-weight: 500\` | Peso médio |
| \`.font-bold\` | \`font-weight: bold\` | Peso negrito |
| \`.text-left\` | \`text-align: left\` | Alinhamento à esquerda |
| \`.text-center\` | \`text-align: center\` | Alinhamento centralizado |
| \`.text-right\` | \`text-align: right\` | Alinhamento à direita |
| \`.text-uppercase\` | \`text-transform: uppercase\` | Texto em maiúsculas |

## Espaçamento

### Margens

| Classe | Valor | Uso |
|--------|-------|-----|
| \`.m-0\` | 0 | Sem margem |
| \`.m-1\` | 0.25rem | Margem muito pequena |
| \`.m-2\` | 0.5rem | Margem pequena |
| \`.m-3\` | 1rem | Margem média |
| \`.m-4\` | 1.5rem | Margem grande |
| \`.m-5\` | 3rem | Margem muito grande |

Também disponível com prefixos direcionais: \`.mt-*\` (top), \`.mb-*\` (bottom), \`.ml-*\` (left), \`.mr-*\` (right), \`.mx-*\` (horizontal), \`.my-*\` (vertical).

### Padding

| Classe | Valor | Uso |
|--------|-------|-----|
| \`.p-0\` | 0 | Sem padding |
| \`.p-1\` | 0.25rem | Padding muito pequeno |
| \`.p-2\` | 0.5rem | Padding pequeno |
| \`.p-3\` | 1rem | Padding médio |
| \`.p-4\` | 1.5rem | Padding grande |
| \`.p-5\` | 3rem | Padding muito grande |

Também disponível com prefixos direcionais: \`.pt-*\` (top), \`.pb-*\` (bottom), \`.pl-*\` (left), \`.pr-*\` (right), \`.px-*\` (horizontal), \`.py-*\` (vertical).

## Componentes

### Cards

\`\`\`python
from utils.style_helpers import get_card_style, get_card_header_style, get_card_body_style

# Exemplo de uso
card = dbc.Card([
    dbc.CardHeader("Título do Card", style=get_card_header_style()),
    dbc.CardBody("Conteúdo do Card", style=get_card_body_style())
], style=get_card_style())
\`\`\`

Ou usando classes CSS:

\`\`\`html
<div class="app-card">
    <div class="app-card-header">Título do Card</div>
    <div class="app-card-body">Conteúdo do Card</div>
</div>
\`\`\`

### Inputs

\`\`\`python
from utils.style_helpers import get_input_style

# Exemplo de uso
input_field = dcc.Input(
    id="input-id",
    type="text",
    value="",
    style=get_input_style()
)

# Para campo somente leitura
readonly_field = dcc.Input(
    id="readonly-id",
    type="text",
    value="Valor somente leitura",
    readOnly=True,
    style=get_input_style(readonly=True)
)
\`\`\`

Ou usando classes CSS:

\`\`\`html
<input class="app-input" type="text" value="">
\`\`\`

### Dropdowns

\`\`\`python
from utils.style_helpers import get_dropdown_style

# Exemplo de uso
dropdown = dcc.Dropdown(
    id="dropdown-id",
    options=[
        {"label": "Opção 1", "value": "1"},
        {"label": "Opção 2", "value": "2"}
    ],
    value="1",
    style=get_dropdown_style()
)
\`\`\`

Ou usando classes CSS:

\`\`\`html
<select class="app-select">
    <option value="1">Opção 1</option>
    <option value="2">Opção 2</option>
</select>
\`\`\`

### Botões

\`\`\`python
from utils.style_helpers import get_button_style

# Exemplo de uso
button = html.Button(
    "Clique Aqui",
    id="button-id",
    style=get_button_style(button_type="primary")
)

# Outros tipos de botão
success_button = html.Button(
    "Sucesso",
    id="success-button-id",
    style=get_button_style(button_type="success")
)

danger_button = html.Button(
    "Perigo",
    id="danger-button-id",
    style=get_button_style(button_type="danger")
)
\`\`\`

Ou usando classes CSS:

\`\`\`html
<button class="app-btn app-btn-primary">Clique Aqui</button>
<button class="app-btn app-btn-success">Sucesso</button>
<button class="app-btn app-btn-danger">Perigo</button>
\`\`\`

### Tabelas

\`\`\`python
from utils.style_helpers import get_table_header_style, get_table_cell_style

# Exemplo de uso
table = html.Table([
    html.Thead([
        html.Tr([
            html.Th("Coluna 1", style=get_table_header_style()),
            html.Th("Coluna 2", style=get_table_header_style())
        ])
    ]),
    html.Tbody([
        html.Tr([
            html.Td("Valor 1", style=get_table_cell_style()),
            html.Td("Valor 2", style=get_table_cell_style())
        ])
    ])
])
\`\`\`

Ou usando classes CSS:

\`\`\`html
<table class="app-table">
    <thead>
        <tr>
            <th>Coluna 1</th>
            <th>Coluna 2</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Valor 1</td>
            <td>Valor 2</td>
        </tr>
    </tbody>
</table>
\`\`\`

### Status

\`\`\`python
from utils.style_helpers import get_status_style

# Exemplo de uso
status = html.Div(
    "Aprovado",
    style=get_status_style("success")
)

# Outros tipos de status
warning_status = html.Div(
    "Atenção",
    style=get_status_style("warning")
)

danger_status = html.Div(
    "Erro",
    style=get_status_style("danger")
)
\`\`\`

Ou usando classes CSS:

\`\`\`html
<div class="app-status app-status-success">Aprovado</div>
<div class="app-status app-status-warning">Atenção</div>
<div class="app-status app-status-danger">Erro</div>
\`\`\`

### Estilos Condicionais

\`\`\`python
from utils.style_helpers import get_conditional_style

# Exemplo de uso
value = 2500  # Valor a ser comparado
threshold = 2000  # Limite para aplicar o estilo

# Aplica estilo de perigo se o valor ultrapassar o limite
cell = html.Td(
    value,
    style=get_conditional_style(value, threshold, style_type="danger")
)
\`\`\`

## Alternância de Tema

A aplicação suporta dois temas: escuro (padrão) e claro. O tema pode ser alternado usando o botão de alternância de tema no cabeçalho da aplicação.

### Implementação em CSS

Para garantir que os componentes respondam à alternância de tema, use os seletores \`body:not(.light-theme)\` para o tema escuro e \`body.light-theme\` para o tema claro:

\`\`\`css
/* Estilo para o tema escuro */
body:not(.light-theme) .my-component {
    background-color: var(--dark-background-card);
    color: var(--dark-text-light);
}

/* Estilo para o tema claro */
body.light-theme .my-component {
    background-color: var(--light-background-card);
    color: var(--light-text-dark);
}
\`\`\`

### Implementação em Python

Para componentes criados dinamicamente em Python, use as funções auxiliares do módulo \`utils.style_helpers\`:

\`\`\`python
from utils.style_helpers import get_card_style

# Exemplo de uso com detecção de tema
def create_card(is_light_theme=False):
    return dbc.Card([
        dbc.CardHeader("Título do Card", style=get_card_header_style(is_light_theme)),
        dbc.CardBody("Conteúdo do Card", style=get_card_body_style(is_light_theme))
    ], style=get_card_style(is_light_theme))
\`\`\`

## Boas Práticas

1. **Use Variáveis CSS**: Sempre use as variáveis CSS definidas em \`theme-dark-vars.css\` e \`theme-light-vars.css\` em vez de valores hardcoded.

2. **Prefira Classes CSS**: Sempre que possível, use classes CSS em vez de estilos inline.

3. **Funções Auxiliares**: Para estilos dinâmicos, use as funções auxiliares definidas em \`utils.style_helpers\`.

4. **Consistência**: Mantenha a consistência visual usando os mesmos estilos para componentes similares.

5. **Acessibilidade**: Garanta contraste adequado entre texto e fundo para melhor legibilidade.

6. **Responsividade**: Use unidades relativas (rem, %) em vez de unidades absolutas (px) para melhor adaptação a diferentes tamanhos de tela.

7. **Documentação**: Documente novos estilos e componentes neste guia de estilos.
`;

        // Function to generate TOC
        function generateTOC(html) {
            const container = document.createElement('div');
            container.innerHTML = html;

            const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const toc = document.getElementById('toc');

            headings.forEach((heading, index) => {
                // Create an ID for the heading if it doesn't have one
                if (!heading.id) {
                    heading.id = `heading-${index}`;
                }

                const level = parseInt(heading.tagName.substring(1));
                const tocItem = document.createElement('li');
                tocItem.className = `toc-h${level}`;

                const link = document.createElement('a');
                link.href = `#${heading.id}`;
                link.textContent = heading.textContent;

                tocItem.appendChild(link);
                toc.appendChild(tocItem);
            });
        }

        // Function to highlight search terms
        function highlightSearchTerms(content, term) {
            if (!term) return content;

            const regex = new RegExp(`(${term})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Function to render markdown
        function renderMarkdown() {
            const searchTerm = document.getElementById('search-input').value.trim();
            let content = markdownContent;

            // Render markdown to HTML
            const renderedHTML = marked.parse(content);

            // Apply search highlighting if there's a search term
            const finalHTML = searchTerm ? highlightSearchTerms(renderedHTML, searchTerm) : renderedHTML;

            // Update content
            document.getElementById('markdown-content').innerHTML = finalHTML;

            // Generate TOC
            generateTOC(finalHTML);

            // Initialize syntax highlighting
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });

            // Render math expressions
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            renderMarkdown();

            // Set up search
            document.getElementById('search-input').addEventListener('input', (e) => {
                renderMarkdown();

                // Scroll to first highlight if there is one
                const firstHighlight = document.querySelector('.highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        });
    </script>
</body>
</html>
