#!/usr/bin/env python3
"""
Utilitário para garantir estrutura consistente dos stores de módulos.
Implementa a estrutura padrão: { 'inputs': {}, 'results': {}, 'transformer_data': {} }
"""

import logging
from typing import Dict, Any, Optional
import copy

log = logging.getLogger(__name__)

# Estrutura padrão para stores de módulos
DEFAULT_MODULE_STORE_STRUCTURE = {
    'inputs': {},
    'results': {},
    'transformer_data': {}
}

def ensure_store_structure(store_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Garante que os dados do store tenham a estrutura padrão.
    
    Args:
        store_data: Dados do store (pode ser None ou dict)
        
    Returns:
        Dict com estrutura garantida
    """
    if store_data is None:
        store_data = {}
    
    # Criar uma cópia para não modificar o original
    result = copy.deepcopy(store_data)
    
    # Garantir que as seções principais existam
    for section in ['inputs', 'results', 'transformer_data']:
        if section not in result:
            result[section] = {}
        elif not isinstance(result[section], dict):
            log.warning(f"Seção '{section}' não é um dict, convertendo para dict vazio")
            result[section] = {}
    
    return result

def get_store_section(store_data: Dict[str, Any], section: str) -> Dict[str, Any]:
    """
    Obtém uma seção específica do store com estrutura garantida.
    
    Args:
        store_data: Dados do store
        section: Nome da seção ('inputs', 'results', 'transformer_data')
        
    Returns:
        Dict da seção solicitada
    """
    structured_data = ensure_store_structure(store_data)
    return structured_data.get(section, {})

def set_store_section(store_data: Dict[str, Any], section: str, section_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Define uma seção específica do store mantendo a estrutura.
    
    Args:
        store_data: Dados do store
        section: Nome da seção ('inputs', 'results', 'transformer_data')
        section_data: Dados para a seção
        
    Returns:
        Dict com a seção atualizada
    """
    structured_data = ensure_store_structure(store_data)
    structured_data[section] = section_data if isinstance(section_data, dict) else {}
    return structured_data

def update_store_section(store_data: Dict[str, Any], section: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """
    Atualiza uma seção específica do store mantendo a estrutura.
    
    Args:
        store_data: Dados do store
        section: Nome da seção ('inputs', 'results', 'transformer_data')
        updates: Atualizações para a seção
        
    Returns:
        Dict com a seção atualizada
    """
    structured_data = ensure_store_structure(store_data)
    
    if isinstance(updates, dict):
        structured_data[section].update(updates)
    else:
        log.warning(f"Updates para seção '{section}' não é um dict, ignorando")
    
    return structured_data

def merge_transformer_data(store_data: Dict[str, Any], transformer_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Mescla dados do transformador na seção transformer_data do store.
    
    Args:
        store_data: Dados do store
        transformer_data: Dados do transformador para mesclar
        
    Returns:
        Dict com transformer_data atualizado
    """
    structured_data = ensure_store_structure(store_data)
    
    if isinstance(transformer_data, dict):
        structured_data['transformer_data'].update(transformer_data)
    else:
        log.warning("transformer_data não é um dict, ignorando merge")
    
    return structured_data

def validate_store_structure(store_data: Dict[str, Any], store_id: str = "unknown") -> bool:
    """
    Valida se o store tem a estrutura correta.
    
    Args:
        store_data: Dados do store
        store_id: ID do store (para logging)
        
    Returns:
        True se a estrutura está correta, False caso contrário
    """
    if not isinstance(store_data, dict):
        log.error(f"Store '{store_id}' não é um dict")
        return False
    
    required_sections = ['inputs', 'results', 'transformer_data']
    missing_sections = []
    invalid_sections = []
    
    for section in required_sections:
        if section not in store_data:
            missing_sections.append(section)
        elif not isinstance(store_data[section], dict):
            invalid_sections.append(section)
    
    if missing_sections:
        log.warning(f"Store '{store_id}' está faltando seções: {missing_sections}")
    
    if invalid_sections:
        log.warning(f"Store '{store_id}' tem seções inválidas (não dict): {invalid_sections}")
    
    return len(missing_sections) == 0 and len(invalid_sections) == 0

def create_module_store(inputs: Optional[Dict[str, Any]] = None, 
                       results: Optional[Dict[str, Any]] = None,
                       transformer_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Cria um store de módulo com a estrutura padrão.
    
    Args:
        inputs: Dados de entrada do módulo
        results: Resultados do módulo
        transformer_data: Dados básicos do transformador
        
    Returns:
        Dict com estrutura de store de módulo
    """
    return {
        'inputs': inputs if isinstance(inputs, dict) else {},
        'results': results if isinstance(results, dict) else {},
        'transformer_data': transformer_data if isinstance(transformer_data, dict) else {}
    }

def log_store_structure(store_data: Dict[str, Any], store_id: str = "unknown"):
    """
    Registra informações sobre a estrutura do store no log.
    
    Args:
        store_data: Dados do store
        store_id: ID do store
    """
    if not isinstance(store_data, dict):
        log.info(f"Store '{store_id}': não é um dict (tipo: {type(store_data)})")
        return
    
    sections_info = []
    for section in ['inputs', 'results', 'transformer_data']:
        if section in store_data:
            section_data = store_data[section]
            if isinstance(section_data, dict):
                sections_info.append(f"{section}: {len(section_data)} campos")
            else:
                sections_info.append(f"{section}: tipo inválido ({type(section_data)})")
        else:
            sections_info.append(f"{section}: ausente")
    
    log.info(f"Store '{store_id}' estrutura: {', '.join(sections_info)}")
