* utilities.css
 * Classes CSS utilitárias para espaçamento, tipografia e layout
 */

/* Espaçamento - Margens */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 1rem !important; }
.ml-4 { margin-left: 1.5rem !important; }
.ml-5 { margin-left: 3rem !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 1rem !important; }
.mr-4 { margin-right: 1.5rem !important; }
.mr-5 { margin-right: 3rem !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; }
.mx-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
.mx-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
.mx-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !important; }
.mx-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !important; }
.my-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }
.my-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
.my-4 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }
.my-5 { margin-top: 3rem !important; margin-bottom: 3rem !important; }

/* Espaçamento - Paddings */
.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pt-5 { padding-top: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 1rem !important; }
.pb-4 { padding-bottom: 1.5rem !important; }
.pb-5 { padding-bottom: 3rem !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 0.25rem !important; }
.pl-2 { padding-left: 0.5rem !important; }
.pl-3 { padding-left: 1rem !important; }
.pl-4 { padding-left: 1.5rem !important; }
.pl-5 { padding-left: 3rem !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 0.25rem !important; }
.pr-2 { padding-right: 0.5rem !important; }
.pr-3 { padding-right: 1rem !important; }
.pr-4 { padding-right: 1.5rem !important; }
.pr-5 { padding-right: 3rem !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
.px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.px-5 { padding-left: 3rem !important; padding-right: 3rem !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

/* Tipografia - Tamanhos */
.text-xs { font-size: 0.7rem !important; }
.text-sm { font-size: 0.75rem !important; }
.text-md { font-size: 0.85rem !important; }
.text-lg { font-size: 0.9rem !important; }
.text-xl { font-size: 1rem !important; }
.text-2xl { font-size: 1.1rem !important; }

/* Tipografia - Pesos */
.font-normal { font-weight: normal !important; }
.font-medium { font-weight: 500 !important; }
.font-bold { font-weight: bold !important; }

/* Tipografia - Alinhamento */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Tipografia - Transformação */
.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }

/* Tipografia - Decoração */
.text-underline { text-decoration: underline !important; }
.text-line-through { text-decoration: line-through !important; }
.text-no-decoration { text-decoration: none !important; }

/* Tipografia - Espaçamento entre letras */
.tracking-tight { letter-spacing: -0.025em !important; }
.tracking-normal { letter-spacing: 0 !important; }
.tracking-wide { letter-spacing: 0.025em !important; }
.tracking-wider { letter-spacing: 0.05em !important; }
.tracking-widest { letter-spacing: 0.1em !important; }

/* Layout - Display */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Layout - Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Layout - Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 1rem !important; }
.gap-4 { gap: 1.5rem !important; }
.gap-5 { gap: 3rem !important; }

/* Layout - Posicionamento */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

/* Layout - Visibilidade */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* Layout - Overflow */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* Bordas */
.border { border: 1px solid !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid !important; }
.border-right { border-right: 1px solid !important; }
.border-bottom { border-bottom: 1px solid !important; }
.border-left { border-left: 1px solid !important; }

.rounded { border-radius: 0.25rem !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-1 { border-radius: 0.125rem !important; }
.rounded-2 { border-radius: 0.25rem !important; }
.rounded-3 { border-radius: 0.5rem !important; }
.rounded-4 { border-radius: 1rem !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 50rem !important; }

/* Sombras */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
.shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

/* Cores de texto para tema escuro */
body:not(.light-theme) .text-primary { color: var(--dark-primary) !important; }
body:not(.light-theme) .text-secondary { color: var(--dark-secondary) !important; }
body:not(.light-theme) .text-success { color: var(--dark-success) !important; }
body:not(.light-theme) .text-danger { color: var(--dark-danger) !important; }
body:not(.light-theme) .text-warning { color: var(--dark-warning) !important; }
body:not(.light-theme) .text-info { color: var(--dark-info) !important; }
body:not(.light-theme) .text-light { color: var(--dark-text-light) !important; }
body:not(.light-theme) .text-dark { color: var(--dark-text-dark) !important; }
body:not(.light-theme) .text-muted { color: var(--dark-text-muted) !important; }

/* Cores de texto para tema claro */
body.light-theme .text-primary { color: var(--light-primary) !important; }
body.light-theme .text-secondary { color: var(--light-secondary) !important; }
body.light-theme .text-success { color: var(--light-success) !important; }
body.light-theme .text-danger { color: var(--light-danger) !important; }
body.light-theme .text-warning { color: var(--light-warning) !important; }
body.light-theme .text-info { color: var(--light-info) !important; }
body.light-theme .text-light { color: var(--light-text-light) !important; }
body.light-theme .text-dark { color: var(--light-text-dark) !important; }
body.light-theme .text-muted { color: var(--light-text-muted) !important; }

/* Cores de fundo para tema escuro */
body:not(.light-theme) .bg-primary { background-color: var(--dark-primary) !important; }
body:not(.light-theme) .bg-secondary { background-color: var(--dark-secondary) !important; }
body:not(.light-theme) .bg-success { background-color: var(--dark-success) !important; }
body:not(.light-theme) .bg-danger { background-color: var(--dark-danger) !important; }
body:not(.light-theme) .bg-warning { background-color: var(--dark-warning) !important; }
body:not(.light-theme) .bg-info { background-color: var(--dark-info) !important; }
body:not(.light-theme) .bg-light { background-color: var(--dark-background-faint) !important; } /* Use faint instead of 'light' */
body:not(.light-theme) .bg-dark { background-color: var(--dark-background-card-header) !important; } /* Use card header instead of 'dark' */

/* Cores de fundo para tema claro */
body.light-theme .bg-primary { background-color: var(--light-primary) !important; }
body.light-theme .bg-secondary { background-color: var(--light-secondary) !important; }
body.light-theme .bg-success { background-color: var(--light-success) !important; }
body.light-theme .bg-danger { background-color: var(--light-danger) !important; }
body.light-theme .bg-warning { background-color: var(--light-warning) !important; }
body.light-theme .bg-info { background-color: var(--light-info) !important; }
body.light-theme .bg-light { background-color: var(--light-background-faint) !important; } /* Use faint */
body.light-theme .bg-dark { background-color: var(--light-background-card-header) !important; } /* Use card header */

/* Cores de borda para tema escuro */
body:not(.light-theme) .border-primary { border-color: var(--dark-primary) !important; }
body:not(.light-theme) .border-secondary { border-color: var(--dark-secondary) !important; }
body:not(.light-theme) .border-success { border-color: var(--dark-success) !important; }
body:not(.light-theme) .border-danger { border-color: var(--dark-danger) !important; }
body:not(.light-theme) .border-warning { border-color: var(--dark-warning) !important; }
body:not(.light-theme) .border-info { border-color: var(--dark-info) !important; }
body:not(.light-theme) .border-light { border-color: var(--dark-border-light) !important; }
body:not(.light-theme) .border-dark { border-color: var(--dark-border-strong) !important; }

/* Cores de borda para tema claro */
body.light-theme .border-primary { border-color: var(--light-primary) !important; }
body.light-theme .border-secondary { border-color: var(--light-secondary) !important; }
body.light-theme .border-success { border-color: var(--light-success) !important; }
body.light-theme .border-danger { border-color: var(--light-danger) !important; }
body.light-theme .border-warning { border-color: var(--light-warning) !important; }
body.light-theme .border-info { border-color: var(--light-info) !important; }
body.light-theme .border-light { border-color: var(--light-border-light) !important; }
body.light-theme .border-dark { border-color: var(--light-border-strong) !important; }
