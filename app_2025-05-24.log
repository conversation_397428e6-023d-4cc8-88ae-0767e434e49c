2025-05-24 13:52:51 - app - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:51 - app - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:51 - app - INFO - Callbacks registrados. Total: 27
2025-05-24 13:52:51 - __main__ - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 215)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 215
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:52:51 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:52:51 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:52:51 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:52:51 - __main__ - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:51 - __main__ - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:51 - __main__ - INFO - Callbacks registrados. Total: 25
2025-05-24 13:52:51 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:52:51 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:52:59 - app - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:59 - app - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:59 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:59 - app - INFO - Callbacks registrados. Total: 27
2025-05-24 13:52:59 - __main__ - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 215)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 215
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:52:59 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:52:59 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:52:59 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:52:59 - __main__ - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:59 - __main__ - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:59 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:59 - __main__ - INFO - Callbacks registrados. Total: 25
2025-05-24 13:52:59 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:52:59 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:53:19 - app - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:53:19 - app - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:53:19 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:53:19 - app - INFO - Callbacks registrados. Total: 27
2025-05-24 13:53:19 - __main__ - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 257)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 257
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:53:19 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:53:19 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:53:19 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:53:19 - __main__ - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:53:19 - __main__ - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:53:19 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:53:19 - __main__ - INFO - Callbacks registrados. Total: 25
2025-05-24 13:53:19 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:53:19 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:53:29 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:53:29 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 13:53:29 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:53:33 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:53:33 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:53:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.08ms
2025-05-24 13:53:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:53:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/dados
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:53:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:53:34 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 13:53:34 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-24 13:53:34 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 13:53:34 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/perdas
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:53:34 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:53:34 - callbacks.global_updates - DEBUG - Painel atualizado em 2.15ms
2025-05-24 13:53:35 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 13:53:36 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:53:36 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:53:36 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:53:36 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:53:36 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/dados
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:53:36 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:53:37 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 13:53:37 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-24 13:53:37 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 13:53:37 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/perdas
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:53:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:53:37 - callbacks.global_updates - DEBUG - Painel atualizado em 1.3ms
2025-05-24 13:53:38 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 13:53:42 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 13:53:42 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:53:44 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-24 13:53:44 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:53:44 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:53:44 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:53:44 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:53:44 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 13:53:44 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-24 13:53:44 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-24 13:53:44 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-24 13:53:44 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-24 13:53:44 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:53:46 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-24 13:53:46 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:53:46 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 13:53:48 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 13:53:48 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-24 13:53:48 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-24 13:53:48 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-24 13:53:48 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:53:50 - callbacks.global_updates - DEBUG - Painel atualizado em 1.5ms
2025-05-24 13:53:52 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:53:54 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-24 13:53:54 - callbacks.global_updates - DEBUG - Painel atualizado em 2.01ms
2025-05-24 13:53:55 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-24 13:53:55 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
