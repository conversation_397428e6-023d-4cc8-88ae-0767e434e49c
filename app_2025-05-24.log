2025-05-24 13:52:51 - app - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:51 - app - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:51 - app - INFO - Callbacks registrados. Total: 27
2025-05-24 13:52:51 - __main__ - ERROR - Erro inesperado ao processar módulo de callback dieletric_analysis: unexpected character after line continuation character (dieletric_analysis.py, line 215)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\dieletric_analysis.py", line 215
    label = f"{valor:.0f} kV ({\'/".join(fonte)})" if fonte else f"{valor:.0f} kV"
                                ^
SyntaxError: unexpected character after line continuation character
2025-05-24 13:52:51 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:52:51 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:52:51 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:52:51 - __main__ - ERROR - Erro ao registrar callbacks de impulse explicitamente: unterminated f-string literal (detected at line 100) (impulse.py, line 100)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\impulse.py", line 100
    except ValueError: log.error(f"Erro ao converter capacitor em 
                                 ^
SyntaxError: unterminated f-string literal (detected at line 100)
2025-05-24 13:52:51 - __main__ - ERROR - Erro ao registrar callbacks de induced_voltage explicitamente: positional argument follows keyword argument (induced_voltage.py, line 662)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\app.py", line 220, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_refatorado\transformer_simulator_v2_complete\callbacks\induced_voltage.py", line 662
    )
    ^
SyntaxError: positional argument follows keyword argument
2025-05-24 13:52:51 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise registrados (callback consolidado).
2025-05-24 13:52:51 - __main__ - INFO - Callbacks registrados. Total: 25
2025-05-24 13:52:51 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:52:51 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

