# callbacks/temperature_rise.py
""" Callbacks para a seção de Elevação de Temperatura. """
from dash import html, Input, Output, State, callback_context, no_update
from utils.callback_helpers import safe_float
import logging
import datetime
from dash.exceptions import PreventUpdate

# Importações da aplicação
from app import app
from config import colors
from utils.routes import normalize_pathname, ROUTE_TEMPERATURE_RISE
from app_core.calculations import (
    calculate_winding_temps,
    calculate_top_oil_rise,
    calculate_thermal_time_constant
)
from utils.store_diagnostics import convert_numpy_types # Importar para salvar no store

log = logging.getLogger(__name__)
log.setLevel(logging.DEBUG)

# --- Callback Consolidado para Elevação de Temperatura ---
@app.callback(
    [
        # Outputs para carregar/atualizar UI
        Output("temp-amb", "value"),
        Output("winding-material", "value"),
        Output("res-cold", "value"),
        Output("temp-cold", "value"),
        Output("res-hot", "value"),
        Output("temp-top-oil", "value"),
        Output("delta-theta-oil-max", "value"),
        Output("avg-winding-temp", "value"),
        Output("avg-winding-rise", "value"),
        Output("top-oil-rise", "value"),
        Output("ptot-used", "value"),
        Output("tau0-result", "value"),
        Output("temp-rise-error-message", "children"),
        # Output para salvar no store
        Output("temperature-rise-store", "data", allow_duplicate=True),
        # Output para painel de info da página
        Output("transformer-info-temperature-rise-page", "children"),
        # Output para log do botão (opcional, pode ser removido se não for útil)
        Output("temp-rise-button-log", "children"),
    ],
    [
        # Triggers principais
        Input("url", "pathname"),
        Input("transformer-inputs-store", "data"),
        Input("temperature-rise-store", "data"),
        Input("losses-store", "data"),
        Input("calc-temp-rise-btn", "n_clicks"),
        # Input do painel global para copiar para a página
        Input("transformer-info-temperature-rise", "children"),
        # Input do botão de debug (opcional)
        Input("calc-temp-rise-debug", "n_clicks"),
    ],
    [
        # States dos inputs para cálculo
        State("temp-amb", "value"),
        State("winding-material", "value"),
        State("res-cold", "value"),
        State("temp-cold", "value"),
        State("res-hot", "value"),
        State("temp-top-oil", "value"),
        # State do store local para evitar sobrescrita desnecessária
        State("temperature-rise-store", "data"),
    ],
    prevent_initial_call=False
)
def consolidated_temperature_rise_callback(
    pathname, transformer_data, stored_temp_rise_data, losses_data, n_clicks_calc,
    global_panel_content, n_clicks_debug,
    # States
    state_temp_amb, state_material, state_res_cold, state_temp_cold, state_res_hot, state_temp_top_oil,
    state_current_store_data
):
    """
    Callback consolidado para o módulo de Elevação de Temperatura.
    Gerencia:
    - Carregamento de dados dos stores (global, local, perdas).
    - Cálculo de elevação de temperatura (acionado por botão ou stores).
    - Atualização da UI com inputs e resultados.
    - Salvamento dos resultados no store local.
    - Atualização do painel de informações na página.
    - Logs de botões (opcional).
    """
    triggered_id = callback_context.triggered_id
    log.info(f"[CONSOLIDATED TempRise] Callback triggered by: {triggered_id}")

    # --- 0. Inicialização e Verificação de Contexto ---
    normalized_path = normalize_pathname(pathname)
    is_temp_rise_page = normalized_path == ROUTE_TEMPERATURE_RISE

    # Se não estiver na página e o trigger não for store ou botão, não faz nada na UI principal
    if not is_temp_rise_page and triggered_id not in [
        "transformer-inputs-store", "temperature-rise-store", "losses-store",
        "calc-temp-rise-btn", "calc-temp-rise-debug"
    ]:
        log.debug(f"[CONSOLIDATED TempRise] Não na página e trigger não relevante ({triggered_id}). Abortando atualização da UI principal.")
        # Ainda atualiza o painel de info e log do botão
        button_log_msg = no_update
        if triggered_id == "calc-temp-rise-btn" and n_clicks_calc:
             timestamp = datetime.datetime.now().strftime("%H:%M:%S")
             button_log_msg = html.Div(f"✅ Calcular pressionado {n_clicks_calc}x (último: {timestamp})", style={"color": "green", "fontSize": "0.8rem"})
        elif triggered_id == "calc-temp-rise-debug" and n_clicks_debug:
             button_log_msg = html.Div(f"🐞 Debug pressionado {n_clicks_debug}x", style={"color": "orange", "fontSize": "0.8rem"})

        return (
            no_update, no_update, no_update, no_update, no_update, no_update, no_update,
            no_update, no_update, no_update, no_update, no_update, no_update,
            no_update, # store
            global_panel_content or "", # info panel
            button_log_msg
        )

    # --- 1. Atualizar Painel de Informações da Página ---
    page_info_panel_content = global_panel_content or ""

    # --- 2. Lógica de Carregamento de Dados --- 
    # Esta parte é similar à callback `temperature_rise_load_data` original
    # Precisa determinar os valores a serem exibidos nos inputs e resultados

    # Lê dados dos stores, tratando None/inválido
    local_data = stored_temp_rise_data if stored_temp_rise_data and isinstance(stored_temp_rise_data, dict) else {}
    global_data = transformer_data if transformer_data and isinstance(transformer_data, dict) else {}
    losses_data_dict = losses_data if losses_data and isinstance(losses_data, dict) else {}

    # Verificar se foi uma limpeza intencional do store local
    if triggered_id == "temperature-rise-store" and isinstance(stored_temp_rise_data, dict) and len(stored_temp_rise_data) == 0:
        log.info("[CONSOLIDATED TempRise] Store local foi limpo. Limpando UI.")
        delta_theta_oil_max_global = None
        if global_data and "transformer_data" in global_data:
            delta_theta_oil_max_global = global_data["transformer_data"].get("elevacao_oleo_topo")
        elif global_data:
            delta_theta_oil_max_global = global_data.get("elevacao_oleo_topo")

        return (
            None, "cobre", None, None, None, None, delta_theta_oil_max_global,
            None, None, None, None, None, "",
            {}, # Limpa o store local também
            page_info_panel_content,
            no_update # log botão
        )

    # Extrair dados aninhados se existirem
    transformer_dict = global_data.get("transformer_data", global_data)
    inputs_local = local_data.get("inputs_temp_rise", local_data)
    results_local = local_data.get("resultados_temp_rise", local_data)

    # Determina delta_theta_oil_max (prioridade global)
    delta_theta_oil_max_final = None
    delta_theta_oil_max_global_raw = transformer_dict.get("elevacao_oleo_topo")
    if delta_theta_oil_max_global_raw is not None:
        delta_theta_oil_max_final = safe_float(delta_theta_oil_max_global_raw)
        if delta_theta_oil_max_final is None:
             log.debug(f"[CONSOLIDATED TempRise] Falha ao converter global elevacao_oleo_topo. Usando local.")
             delta_theta_oil_max_final = safe_float(inputs_local.get("input_delta_theta_oil_max"))
    else:
        delta_theta_oil_max_final = safe_float(inputs_local.get("input_delta_theta_oil_max"))

    if delta_theta_oil_max_final is None: delta_theta_oil_max_final = 55.0 # Padrão

    # Valores a serem exibidos na UI (inicialmente carregados do store local)
    ui_temp_amb = inputs_local.get("input_ta")
    ui_material = inputs_local.get("input_material", "cobre")
    ui_res_cold = inputs_local.get("input_rc")
    ui_temp_cold = inputs_local.get("input_tc")
    ui_res_hot = inputs_local.get("input_rw")
    ui_temp_top_oil = inputs_local.get("input_t_oil")
    ui_delta_theta_oil_max = delta_theta_oil_max_final # Prioridade global já tratada

    ui_avg_winding_temp = results_local.get("avg_winding_temp")
    ui_avg_winding_rise = results_local.get("avg_winding_rise")
    ui_top_oil_rise = results_local.get("top_oil_rise")
    ui_ptot_used = results_local.get("ptot_used_kw")
    ui_tau0_result = results_local.get("tau0_h")
    ui_error_message = ""
    message_str = results_local.get("message", "")
    if message_str:
         is_warning = "Aviso" in message_str
         ui_error_message = html.Div(message_str, style={"color": colors.get("warning", "orange") if is_warning else colors.get("fail", "red"), "fontSize": "0.7rem"})

    # --- 3. Lógica de Cálculo (se botão foi clicado ou stores relevantes atualizados) ---
    store_data_to_save = no_update # Por padrão, não salva no store
    button_log_msg = no_update

    # Condições para recalcular: botão clicado OU atualização dos stores de input (global/losses)
    should_recalculate = False
    if triggered_id == "calc-temp-rise-btn" and n_clicks_calc:
        should_recalculate = True
        log.info(f"🔥 Botão Calcular pressionado ({n_clicks_calc}x). Recalculando...")
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        button_log_msg = html.Div(f"✅ Calcular pressionado {n_clicks_calc}x (último: {timestamp})", style={"color": "green", "fontSize": "0.8rem"})
    elif triggered_id in ["transformer-inputs-store", "losses-store"]:
        # Recalcula automaticamente se os dados de entrada mudarem
        should_recalculate = True
        log.info(f"Store de input ({triggered_id}) atualizado. Recalculando elevação...")
    elif triggered_id == "calc-temp-rise-debug" and n_clicks_debug:
        log.info(f"🐞 Botão Debug pressionado ({n_clicks_debug}x). Não recalcula, apenas log.")
        button_log_msg = html.Div(f"🐞 Debug pressionado {n_clicks_debug}x", style={"color": "orange", "fontSize": "0.8rem"})

    if should_recalculate:
        # Usa os valores dos STATES (que refletem a UI no momento do clique/trigger)
        temp_amb = safe_float(state_temp_amb)
        material = state_material
        res_cold = safe_float(state_res_cold)
        temp_cold = safe_float(state_temp_cold)
        res_hot = safe_float(state_res_hot)
        temp_top_oil = safe_float(state_temp_top_oil)
        delta_theta_oil_max_calc = ui_delta_theta_oil_max # Usa o valor já determinado

        # Pega perdas totais do store de perdas
        ptot_kw = None
        if losses_data_dict:
            results_losses = losses_data_dict.get("resultados_perdas_carga", losses_data_dict)
            ptot_kw = safe_float(results_losses.get("ptot_kw"))

        # Pega dados do transformador do store global
        peso_oleo_kg = safe_float(transformer_dict.get("peso_oleo")) * 1000 if transformer_dict.get("peso_oleo") else None
        peso_parte_ativa_kg = safe_float(transformer_dict.get("peso_parte_ativa")) * 1000 if transformer_dict.get("peso_parte_ativa") else None

        log.debug(f"[CALC TempRise] Inputs: Ta={temp_amb}, Mat={material}, Rc={res_cold}, Tc={temp_cold}, Rw={res_hot}, T_oil={temp_top_oil}, DeltaOilMax={delta_theta_oil_max_calc}, Ptot={ptot_kw}, PesoOleo={peso_oleo_kg}, PesoPA={peso_parte_ativa_kg}")

        # Realiza os cálculos
        message = ""
        avg_winding_temp, avg_winding_rise = None, None
        top_oil_rise = None
        tau0_h = None

        try:
            if all(v is not None for v in [res_cold, temp_cold, res_hot, temp_amb]):
                avg_winding_temp, avg_winding_rise = calculate_winding_temps(
                    res_cold, temp_cold, res_hot, temp_amb, material
                )
                log.info(f"[CALC TempRise] Winding Temps: Avg={avg_winding_temp}, Rise={avg_winding_rise}")
            else:
                message += "Aviso: Dados insuficientes para calcular Temp. Enrolamento. "

            if temp_top_oil is not None and temp_amb is not None:
                top_oil_rise = calculate_top_oil_rise(temp_top_oil, temp_amb)
                log.info(f"[CALC TempRise] Top Oil Rise: {top_oil_rise}")
            else:
                message += "Aviso: Dados insuficientes para calcular Elevação Óleo Topo. "

            if ptot_kw is not None and top_oil_rise is not None and peso_oleo_kg is not None and peso_parte_ativa_kg is not None:
                tau0_h = calculate_thermal_time_constant(
                    ptot_kw, top_oil_rise, peso_oleo_kg, peso_parte_ativa_kg
                )
                log.info(f"[CALC TempRise] Thermal Time Constant: {tau0_h}")
            else:
                missing_tau = []
                if ptot_kw is None: missing_tau.append("Perdas Totais")
                if top_oil_rise is None: missing_tau.append("Elevação Óleo Topo")
                if peso_oleo_kg is None: missing_tau.append("Peso Óleo")
                if peso_parte_ativa_kg is None: missing_tau.append("Peso Parte Ativa")
                if missing_tau:
                    message += f"Aviso: Dados insuficientes para calcular Constante Térmica ({', '.join(missing_tau)}). "

            # Atualiza UI com novos resultados
            ui_avg_winding_temp = f"{avg_winding_temp:.2f}" if avg_winding_temp is not None else None
            ui_avg_winding_rise = f"{avg_winding_rise:.2f}" if avg_winding_rise is not None else None
            ui_top_oil_rise = f"{top_oil_rise:.2f}" if top_oil_rise is not None else None
            ui_ptot_used = f"{ptot_kw:.2f}" if ptot_kw is not None else None
            ui_tau0_result = f"{tau0_h:.2f}" if tau0_h is not None else None

            if message:
                ui_error_message = html.Div(message.strip(), style={"color": colors.get("warning", "orange"), "fontSize": "0.7rem"})
            else:
                ui_error_message = ""

            # Prepara dados para salvar no store
            inputs_to_save = {
                "input_ta": temp_amb,
                "input_material": material,
                "input_rc": res_cold,
                "input_tc": temp_cold,
                "input_rw": res_hot,
                "input_t_oil": temp_top_oil,
                "input_delta_theta_oil_max": delta_theta_oil_max_calc # Salva o valor usado
            }
            results_to_save = {
                "avg_winding_temp": avg_winding_temp,
                "avg_winding_rise": avg_winding_rise,
                "top_oil_rise": top_oil_rise,
                "ptot_used_kw": ptot_kw,
                "tau0_h": tau0_h,
                "message": message.strip(),
                "timestamp": datetime.datetime.now().isoformat()
            }
            store_data_to_save = {
                "inputs_temp_rise": inputs_to_save,
                "resultados_temp_rise": results_to_save
            }
            store_data_to_save = convert_numpy_types(store_data_to_save)
            log.info(f"[CONSOLIDATED TempRise] Preparando para salvar no store: {store_data_to_save}")

        except Exception as e:
            log.exception("[CONSOLIDATED TempRise] Erro durante o cálculo")
            ui_error_message = html.Div(f"Erro no cálculo: {e}", style={"color": colors.get("fail", "red"), "fontSize": "0.7rem"})
            # Limpa resultados em caso de erro
            ui_avg_winding_temp, ui_avg_winding_rise, ui_top_oil_rise, ui_ptot_used, ui_tau0_result = None, None, None, None, None
            store_data_to_save = no_update # Não salva se deu erro

    # --- 4. Retornar todos os outputs --- 
    # Usa os valores prefixados com 'ui_' que foram carregados ou calculados
    return (
        ui_temp_amb, ui_material, ui_res_cold, ui_temp_cold, ui_res_hot, ui_temp_top_oil, ui_delta_theta_oil_max,
        ui_avg_winding_temp, ui_avg_winding_rise, ui_top_oil_rise, ui_ptot_used, ui_tau0_result, ui_error_message,
        store_data_to_save,
        page_info_panel_content,
        button_log_msg
    )

# Remover ou comentar os callbacks individuais antigos
# @app.callback(...) def update_temperature_rise_page_info_panel(...)
# @app.callback(...) def temperature_rise_load_data(...)
# @app.callback(...) def log_button_press(...)
# @app.callback(...) def debug_calc_button(...)
# @app.callback(...) def temperature_rise_calculate(...)

# Função de registro explícito (opcional, se app.py não usar mais decoradores diretamente)
def register_temperature_rise_callbacks(app_instance):
    # A lógica do callback consolidado já está registrada com @app.callback
    # Esta função pode ser usada para garantir que o módulo foi importado
    # ou para registrar callbacks adicionais se necessário no futuro.
    log.info("Callbacks do módulo temperature_rise registrados (callback consolidado).")
    pass

