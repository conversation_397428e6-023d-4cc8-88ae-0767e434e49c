"""
Adaptador para integrar os callbacks existentes com o novo sistema de persistência e propagação.
Este módulo garante que a lógica dos cálculos dos callbacks permaneça intacta.
"""

import logging
from typing import Dict, Any, Callable, List, Optional

log = logging.getLogger(__name__)

def wrap_callback_with_persistence(app, callback_function: Callable, store_id: str) -> Callable:
    """
    Envolve um callback existente com funcionalidade de persistência automática.

    Args:
        app: Instância da aplicação Dash
        callback_function: Função de callback original
        store_id: ID do store associado ao callback

    Returns:
        Callable: Função de callback envolvida com persistência
    """
    def wrapped_callback(*args, **kwargs):
        # Executar o callback original
        result = callback_function(*args, **kwargs)

        # Se o resultado for um dicionário e o state_manager estiver disponível,
        # salvar os dados no MCP e propagar para outros stores
        if isinstance(result, dict) and hasattr(app, 'state_manager') and app.state_manager is not None:
            try:
                # Salvar dados no state_manager com propagação automática
                app.state_manager.set_store(store_id, result)
                log.debug(f"[wrap_callback_with_persistence] Dados do callback salvos e propagados para store '{store_id}'")
            except Exception as e:
                log.error(f"[wrap_callback_with_persistence] Erro ao salvar/propagar dados do callback: {e}")

        # Retornar o resultado original
        return result

    # Preservar metadados da função original
    wrapped_callback.__name__ = callback_function.__name__
    wrapped_callback.__doc__ = callback_function.__doc__

    return wrapped_callback

def adapt_transformer_inputs_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de transformer_inputs para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    def adapted_callback(*args, **kwargs):
        # Executar o callback original
        result = original_callback(*args, **kwargs)

        # Se o resultado for uma tupla (como esperado para o callback de transformer_inputs)
        # e o state_manager estiver disponível, atualizar os dados no MCP
        if isinstance(result, tuple) and hasattr(app, 'state_manager') and app.state_manager is not None:
            try:
                # Obter dados atuais do transformer-inputs-store
                current_data = app.state_manager.get_store("transformer-inputs-store") or {}

                # Atualizar correntes nominais
                corrente_at, corrente_bt, corrente_terciario, corrente_at_tap_maior, corrente_at_tap_menor = result

                # Converter para float se possível
                try:
                    current_data["corrente_nominal_at"] = float(corrente_at) if corrente_at else None
                except (ValueError, TypeError):
                    current_data["corrente_nominal_at"] = corrente_at

                try:
                    current_data["corrente_nominal_bt"] = float(corrente_bt) if corrente_bt else None
                except (ValueError, TypeError):
                    current_data["corrente_nominal_bt"] = corrente_bt

                try:
                    current_data["corrente_nominal_terciario"] = float(corrente_terciario) if corrente_terciario else None
                except (ValueError, TypeError):
                    current_data["corrente_nominal_terciario"] = corrente_terciario

                try:
                    current_data["corrente_nominal_at_tap_maior"] = float(corrente_at_tap_maior) if corrente_at_tap_maior else None
                except (ValueError, TypeError):
                    current_data["corrente_nominal_at_tap_maior"] = corrente_at_tap_maior

                try:
                    current_data["corrente_nominal_at_tap_menor"] = float(corrente_at_tap_menor) if corrente_at_tap_menor else None
                except (ValueError, TypeError):
                    current_data["corrente_nominal_at_tap_menor"] = corrente_at_tap_menor

                # Salvar dados atualizados no state_manager
                app.state_manager.set_store("transformer-inputs-store", current_data)
                log.debug("[adapt_transformer_inputs_callback] Dados de correntes nominais atualizados e propagados")
            except Exception as e:
                log.error(f"[adapt_transformer_inputs_callback] Erro ao atualizar/propagar dados: {e}")

        # Retornar o resultado original
        return result

    # Preservar metadados da função original
    adapted_callback.__name__ = original_callback.__name__
    adapted_callback.__doc__ = original_callback.__doc__

    return adapted_callback

def adapt_losses_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de losses para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    def adapted_callback(*args, **kwargs):
        # Executar o callback original
        result = original_callback(*args, **kwargs)

        # Se o resultado for um dicionário e o state_manager estiver disponível,
        # salvar os dados no MCP e propagar para outros stores
        if isinstance(result, dict) and hasattr(app, 'state_manager') and app.state_manager is not None:
            try:
                # Obter dados atuais do losses-store
                current_data = app.state_manager.get_store("losses-store") or {}

                # Mesclar com os novos dados
                for key, value in result.items():
                    current_data[key] = value

                # Salvar dados atualizados no state_manager
                app.state_manager.set_store("losses-store", current_data)
                log.debug("[adapt_losses_callback] Dados de perdas atualizados e propagados")
            except Exception as e:
                log.error(f"[adapt_losses_callback] Erro ao atualizar/propagar dados: {e}")

        # Retornar o resultado original
        return result

    # Preservar metadados da função original
    adapted_callback.__name__ = original_callback.__name__
    adapted_callback.__doc__ = original_callback.__doc__

    return adapted_callback

def adapt_impulse_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de impulse para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "impulse-store")

def adapt_dieletric_analysis_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de dieletric_analysis para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "dieletric-analysis-store")

def adapt_applied_voltage_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de applied_voltage para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "applied-voltage-store")

def adapt_induced_voltage_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de induced_voltage para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "induced-voltage-store")

def adapt_short_circuit_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de short_circuit para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "short-circuit-store")

def adapt_temperature_rise_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de temperature_rise para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "temperature-rise-store")

def adapt_comprehensive_analysis_callback(app, original_callback: Callable) -> Callable:
    """
    Adapta o callback de comprehensive_analysis para o novo sistema de persistência.

    Args:
        app: Instância da aplicação Dash
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    return wrap_callback_with_persistence(app, original_callback, "comprehensive-analysis-store")

# Mapeamento de módulos para funções de adaptação
ADAPTER_MAP = {
    "transformer_inputs": adapt_transformer_inputs_callback,
    "losses": adapt_losses_callback,
    "impulse": adapt_impulse_callback,
    "dieletric_analysis": adapt_dieletric_analysis_callback,
    "applied_voltage": adapt_applied_voltage_callback,
    "induced_voltage": adapt_induced_voltage_callback,
    "short_circuit": adapt_short_circuit_callback,
    "temperature_rise": adapt_temperature_rise_callback,
    "comprehensive_analysis": adapt_comprehensive_analysis_callback,
}

def adapt_callback(app, module_name: str, original_callback: Callable) -> Callable:
    """
    Adapta um callback para o novo sistema de persistência com base no módulo.

    Args:
        app: Instância da aplicação Dash
        module_name: Nome do módulo
        original_callback: Função de callback original

    Returns:
        Callable: Função de callback adaptada
    """
    adapter = ADAPTER_MAP.get(module_name)
    if adapter:
        return adapter(app, original_callback)
    else:
        # Se não houver adaptador específico, usar o adaptador genérico
        log.warning(f"[adapt_callback] Adaptador específico não encontrado para módulo '{module_name}'. Usando adaptador genérico.")
        return original_callback
