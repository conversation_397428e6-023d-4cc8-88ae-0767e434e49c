# callbacks/dielectric_analysis_comprehensive.py
"""
Callbacks para a seção de Análise Dielétrica Completa.
"""
import logging
import time

import dash_bootstrap_components as dbc
from dash import Input, Output, State, callback, html, no_update
from dash.exceptions import PreventUpdate

from components.ui_elements import create_comparison_table
from components.validators import safe_float

# Importar estilos padronizados
from layouts import COLORS


log = logging.getLogger(__name__)


# --- Helper Functions ---
# Implementação simplificada do VerificadorTransformador para garantir que a análise funcione
class VerificadorTransformadorSimplificado:
    """Implementação simplificada do VerificadorTransformador para garantir que a análise funcione."""

    def __init__(self):
        """Inicializa o verificador simplificado."""
        log.info("[VERIFICADOR SIMPLIFICADO] Inicializando verificador simplificado")
        self.nbr = None  # Será inicializado sob demanda

    def is_valid(self):
        """Verifica se o verificador é válido."""
        return True

    def get_all_insulation_options(self, um_value):
        """Retorna todas as opções de isolamento para um valor de Um."""
        log.info(f"[VERIFICADOR SIMPLIFICADO] Obtendo opções de isolamento para Um={um_value}")

        # Criar opções padrão para NBR/IEC
        # Garantir que um_value seja tratado como float para cálculos
        um_float = float(um_value)

        nbr_option = {
            "norma": "NBR/IEC",
            "um_kv": um_value,
            "bil_kvp": self._get_default_bil(um_value),
            "lic_kvp": self._get_default_lic(um_value),
            "sil_kvp": self._get_default_sil(um_value),  # Pode ser 'NA' ou um número
            "acsd_kv_rms": [self._get_default_acsd(um_value)],
            "acld_kv_rms": [self._get_default_acld(um_value)],
            "pd_requerido": um_float >= 72.5,
            "distancias_mm": {
                "fase_terra": 100 + um_float * 2,
                "fase_fase": 150 + um_float * 2.5,
                "outro_enrolamento": 200 + um_float * 3,
                "ref_norma": "NBR 5356-3",
                "chave_busca": "BIL",
                "valor_busca": self._get_default_bil(um_value),
            },
        }

        # Criar opções padrão para IEEE
        # Obter valores com tratamento para 'NA'
        sil_value = self._get_default_sil(um_value)
        if sil_value == "NA":
            ieee_sil = "NA"  # Manter como 'NA' se for não aplicável
        else:
            ieee_sil = sil_value * 1.1  # Multiplicar por 1.1 se for um número

        ieee_option = {
            "norma": "IEEE",
            "um_kv": um_value,
            "bil_kvp": self._get_default_bil(um_value) * 1.1,  # IEEE geralmente tem BIL maior
            "lic_kvp": self._get_default_lic(um_value) * 1.1,
            "sil_kvp": ieee_sil,
            "acsd_kv_rms": [self._get_default_acsd(um_value) * 1.1],
            "acld_kv_rms": [self._get_default_acld(um_value) * 1.1],
            "pd_requerido": um_float >= 72.5,  # Usar um_float que já foi convertido
            "distancias_mm": {
                "fase_terra": 120 + um_float * 2.2,  # Usar um_float que já foi convertido
                "fase_fase": 170 + um_float * 2.7,  # Usar um_float que já foi convertido
                "outro_enrolamento": 220 + um_float * 3.2,  # Usar um_float que já foi convertido
                "ref_norma": "IEEE C57.12.00",
                "chave_busca": "BIL",
                "valor_busca": self._get_default_bil(um_value) * 1.1,
            },
        }

        return [nbr_option, ieee_option]

    def get_test_sequences_from_profiles(self, um_key):
        """Retorna as sequências de teste para um valor de Um."""
        log.info(f"[VERIFICADOR SIMPLIFICADO] Obtendo sequências de teste para Um={um_key}")

        # Criar sequências padrão
        sequences = {
            "ACSD_FaseTerra": {
                "title": "Sequência de Ensaio de Tensão Aplicada Curta Duração (ACSD) - Fase-Terra",
                "steps": [
                    {"time": 0, "voltage": 0, "label": "Início"},
                    {"time": 10, "voltage": 100, "label": "Tensão Nominal"},
                    {"time": 20, "voltage": 0, "label": "Fim"},
                ],
            },
            "ACSD_FaseFase": {
                "title": "Sequência de Ensaio de Tensão Aplicada Curta Duração (ACSD) - Fase-Fase",
                "steps": [
                    {"time": 0, "voltage": 0, "label": "Início"},
                    {"time": 10, "voltage": 100, "label": "Tensão Nominal"},
                    {"time": 20, "voltage": 0, "label": "Fim"},
                ],
            },
            "ACLD": {
                "title": "Sequência de Ensaio de Tensão Induzida Longa Duração (ACLD)",
                "steps": [
                    {"time": 0, "voltage": 0, "label": "Início"},
                    {"time": 10, "voltage": 80, "label": "U2 (80%)"},
                    {"time": 20, "voltage": 100, "label": "Tensão Nominal"},
                    {"time": 80, "voltage": 100, "label": "Manutenção"},
                    {"time": 90, "voltage": 0, "label": "Fim"},
                ],
            },
        }

        return sequences

    def get_required_tests_comparison(self, um_value):
        """Retorna uma comparação dos testes requeridos para um valor de Um."""
        log.info(f"[VERIFICADOR SIMPLIFICADO] Obtendo comparação de testes para Um={um_value}")

        # Garantir que um_value seja tratado como float para comparações
        try:
            um_float = float(um_value)
            log.info(f"[VERIFICADOR SIMPLIFICADO] Um convertido para float: {um_float}")
        except (ValueError, TypeError) as e:
            log.error(f"[VERIFICADOR SIMPLIFICADO] Erro ao converter Um para float: {e}")
            um_float = 0  # Valor padrão seguro

        # Determinar quais testes são requeridos com base no valor de Um
        nbr_tests = {
            "li": "Requerido",
            "lic": "Requerido" if um_float >= 72.5 else "Opcional",
            "si": "Requerido" if um_float >= 300 else "Não aplicável",
            "acsd": "Requerido",
            "acld": "Requerido" if um_float >= 72.5 else "Opcional",
            "pd": "Requerido" if um_float >= 72.5 else "Não aplicável",
        }

        ieee_tests = {
            "li": "Requerido",
            "lic": "Requerido" if um_float >= 72.5 else "Opcional",
            "si": "Requerido" if um_float >= 300 else "Não aplicável",
            "acsd": "Requerido",
            "acld": "Requerido" if um_float >= 72.5 else "Opcional",
            "pd": "Requerido" if um_float >= 72.5 else "Não aplicável",
        }

        return {"NBR": nbr_tests, "IEEE": ieee_tests}

    def _get_default_bil(self, um_value):
        """Retorna um valor padrão de BIL para um valor de Um."""
        um_float = float(um_value)
        if um_float <= 24:
            return 125
        elif um_float <= 36:
            return 170
        elif um_float <= 72.5:
            return 325
        elif um_float <= 145:
            return 650
        elif um_float <= 245:
            return 1050
        elif um_float <= 420:
            return 1425
        else:
            return 1550

    def _get_default_lic(self, um_value):
        """Retorna um valor padrão de LIC para um valor de Um."""
        return self._get_default_bil(um_value) * 1.1

    def _get_default_sil(self, um_value):
        """Retorna um valor padrão de SIL para um valor de Um."""
        um_float = float(um_value)
        if um_float >= 300:
            return self._get_default_bil(um_value) * 0.75
        return "NA"  # String 'NA' para indicar "Não Aplicável"

    def _get_default_acsd(self, um_value):
        """Retorna um valor padrão de ACSD para um valor de Um."""
        um_float = float(um_value)
        if um_float <= 1.1:
            return 3
        elif um_float <= 3.6:
            return 10
        elif um_float <= 7.2:
            return 20
        elif um_float <= 12:
            return 28
        elif um_float <= 24:
            return 50
        elif um_float <= 36:
            return 70
        elif um_float <= 52:
            return 95
        elif um_float <= 72.5:
            return 140
        elif um_float <= 100:
            return 185
        elif um_float <= 123:
            return 230
        elif um_float <= 145:
            return 275
        elif um_float <= 170:
            return 325
        elif um_float <= 245:
            return 460
        elif um_float <= 300:
            return 570
        elif um_float <= 362:
            return 680
        elif um_float <= 420:
            return 800
        elif um_float <= 550:
            return 970
        else:
            return 1100

    def _get_default_acld(self, um_value):
        """Retorna um valor padrão de ACLD para um valor de Um."""
        return float(um_value) * 2


def get_verificador_instance():
    """Obtém uma instância do VerificadorTransformador."""
    try:
        log.info("[VERIFICADOR] Tentando criar instância do VerificadorTransformador")

        # Usar a implementação simplificada para garantir que a análise funcione
        verificador = VerificadorTransformadorSimplificado()
        log.info("[VERIFICADOR] Instância simplificada criada")
        return verificador

    except Exception as e:
        log.exception(f"[VERIFICADOR] Error creating VerificadorTransformador: {e}")
        return None


# --- Callback para forçar o carregamento dos dados ---
@callback(
    Output("dieletric-analysis-store", "data", allow_duplicate=True),
    Input("forcar-carregamento-button", "n_clicks"),
    State("transformer-inputs-store", "data"),
    prevent_initial_call=True,
)
def force_load_dielectric_data(n_clicks, transformer_data):
    """
    Força o carregamento dos dados da análise dielétrica.

    Args:
        n_clicks: Número de cliques no botão
        transformer_data: Dados do transformador

    Returns:
        data: Dados da análise dielétrica
    """
    if n_clicks is None or n_clicks == 0:
        raise PreventUpdate

    log.info(
        f"[FORCE LOAD] Forçando carregamento dos dados da análise dielétrica. n_clicks: {n_clicks}"
    )
    log.info(f"[FORCE LOAD] Tipo de transformer_data: {type(transformer_data)}")
    log.info(f"[FORCE LOAD] Conteúdo de transformer_data: {transformer_data}")

    # Se transformer_data estiver vazio, retornar um dicionário vazio
    if not transformer_data or not isinstance(transformer_data, dict):
        log.warning(
            "[FORCE LOAD] Dados do transformador inválidos ou vazios. Retornando dicionário vazio."
        )
        transformer_data = {}

    # Função auxiliar para formatar valores com tratamento de erros robusto
    def safe_str_output(value, default=None):
        if value is None or value == "":
            return default
        try:
            # Tenta converter para float
            f_val = float(str(value).replace(",", ".").strip())
            # Se for um número inteiro, retorna como inteiro
            if abs(f_val - round(f_val)) < 1e-10:
                return str(int(f_val))
            # Senão retorna com uma casa decimal
            return f"{f_val:.1f}"
        except (ValueError, TypeError, AttributeError) as e:
            log.warning(f"Erro ao converter valor '{value}': {e}")
            # Se não conseguir converter, retorna o valor como string ou o default
            if isinstance(value, (str, int, float)):
                return str(value)
            return default

    # Extrair dados do transformador sem valores padrão
    um_at = safe_str_output(transformer_data.get("classe_tensao_at"))
    um_bt = safe_str_output(transformer_data.get("classe_tensao_bt"))
    um_terciario = safe_str_output(transformer_data.get("classe_tensao_terciario"))

    # Determinar conexões - versão simplificada para evitar erros
    def determine_connection(conn_key, neutral_volt_key):
        try:
            connection_stored = transformer_data.get(conn_key, "")

            # Valores padrão para cada tipo de enrolamento
            if "at" in conn_key:
                default_conn = "YN"  # Alta tensão geralmente é estrela com neutro
            elif "bt" in conn_key:
                default_conn = "D"  # Baixa tensão geralmente é triângulo
            else:
                default_conn = "Y"  # Terciário geralmente é estrela

            # Se não tiver valor ou não for string, usa o padrão
            if not connection_stored or not isinstance(connection_stored, str):
                return default_conn

            # Converte para minúsculo para comparação
            conn_lower = connection_stored.lower()

            # Lógica simplificada de determinação
            if "triangulo" in conn_lower or "delta" in conn_lower or conn_lower.startswith("d"):
                return "D"
            elif "estrela" in conn_lower or "star" in conn_lower or conn_lower.startswith("y"):
                # Verifica se tem neutro
                neutral_voltage = transformer_data.get(neutral_volt_key)
                if (
                    neutral_voltage
                    and str(neutral_voltage).strip()
                    and float(str(neutral_voltage).strip() or 0) > 0
                ):
                    return "YN"
                return "Y"
            else:
                return default_conn
        except Exception as e:
            log.exception(f"Erro ao determinar conexão para {conn_key}: {e}")
            # Em caso de erro, retorna um valor padrão seguro
            return "YN" if "at" in conn_key else "D" if "bt" in conn_key else "Y"

    conexao_at = determine_connection("conexao_at", "tensao_bucha_neutro_at")
    conexao_bt = determine_connection("conexao_bt", "tensao_bucha_neutro_bt")
    conexao_terciario = determine_connection("conexao_terciario", "tensao_bucha_neutro_terciario")

    # Obter valores de Tensão do Neutro
    neutro_um_at = safe_str_output(transformer_data.get("tensao_bucha_neutro_at"))
    neutro_um_bt = safe_str_output(transformer_data.get("tensao_bucha_neutro_bt"))
    neutro_um_terciario = safe_str_output(transformer_data.get("tensao_bucha_neutro_terciario"))

    # Obter valores de NBI/IA com valores padrão baseados na classe de tensão
    def get_default_nbi(um_value):
        try:
            um_float = float(str(um_value).replace(",", ".").strip())
            # Valores padrão aproximados baseados na classe de tensão
            if um_float <= 24:
                return "125"
            elif um_float <= 36:
                return "170"
            elif um_float <= 72.5:
                return "325"
            elif um_float <= 145:
                return "650"
            elif um_float <= 245:
                return "1050"
            elif um_float <= 420:
                return "1425"
            else:
                return "1550"
        except:
            return "1050"  # Valor padrão seguro

    # Obter valores de NBI/IA sem valores padrão
    nbi_at = safe_str_output(transformer_data.get("nbi_at"))
    nbi_bt = safe_str_output(transformer_data.get("nbi_bt"))
    nbi_terciario = safe_str_output(transformer_data.get("nbi_terciario"))

    # Obter valores de NBI Neutro (60% do NBI principal para YN)
    def get_neutro_nbi(nbi_value, conexao):
        if conexao != "YN" or not nbi_value:
            return None
        try:
            nbi_float = float(str(nbi_value).replace(",", ".").strip())
            return f"{nbi_float * 0.6:.1f}"
        except:
            return None

    nbi_neutro_at = safe_str_output(transformer_data.get("nbi_neutro_at"))
    nbi_neutro_bt = safe_str_output(transformer_data.get("nbi_neutro_bt"))
    nbi_neutro_terciario = safe_str_output(transformer_data.get("nbi_neutro_terciario"))

    # Obter valores de SIL/IM (para AT geralmente é 75-80% do NBI)
    def get_default_sil(nbi_value, um_value):
        try:
            um_float = float(str(um_value).replace(",", ".").strip())
            nbi_float = float(str(nbi_value).replace(",", ".").strip())

            # SIL só é aplicável para tensões acima de 300kV geralmente
            if um_float >= 300:
                return f"{nbi_float * 0.75:.1f}"
            return "0"  # Não aplicável
        except:
            return "0"  # Não aplicável

    sil_at = safe_str_output(transformer_data.get("sil_at"))
    sil_bt = safe_str_output(transformer_data.get("sil_bt"))
    sil_terciario = safe_str_output(transformer_data.get("sil_terciario"))

    # Função para calcular IAC de forma segura
    def calculate_iac(nbi_value):
        if not nbi_value:
            return None
        try:
            # Tenta converter para float com tratamento de erros
            nbi_float = float(str(nbi_value).replace(",", ".").strip())
            # Calcula IAC como 1.1 vezes o valor de NBI
            return f"{nbi_float * 1.1:.1f}"
        except (ValueError, TypeError, AttributeError) as e:
            log.warning(f"Erro ao calcular IAC para NBI '{nbi_value}': {e}")
            return None

    # Calcular IAC para cada enrolamento
    iac_at = calculate_iac(nbi_at)
    iac_bt = calculate_iac(nbi_bt)
    iac_terciario = calculate_iac(nbi_terciario)

    # Calcular valores padrão para Tensão Aplicada baseados na classe de tensão
    def get_default_tensao_aplicada(um_value):
        try:
            um_float = float(str(um_value).replace(",", ".").strip())
            # Valores aproximados baseados na norma
            if um_float <= 1.1:
                return "3"
            elif um_float <= 3.6:
                return "10"
            elif um_float <= 7.2:
                return "20"
            elif um_float <= 12:
                return "28"
            elif um_float <= 24:
                return "50"
            elif um_float <= 36:
                return "70"
            elif um_float <= 52:
                return "95"
            elif um_float <= 72.5:
                return "140"
            elif um_float <= 100:
                return "185"
            elif um_float <= 123:
                return "230"
            elif um_float <= 145:
                return "275"
            elif um_float <= 170:
                return "325"
            elif um_float <= 245:
                return "460"
            elif um_float <= 300:
                return "570"
            elif um_float <= 362:
                return "680"
            elif um_float <= 420:
                return "800"
            elif um_float <= 550:
                return "970"
            else:
                return "1100"
        except:
            return "460"  # Valor padrão seguro

    # Obter valores de Tensão Aplicada sem valores padrão
    tensao_aplicada_at = safe_str_output(transformer_data.get("teste_tensao_aplicada_at"))
    tensao_aplicada_bt = safe_str_output(transformer_data.get("teste_tensao_aplicada_bt"))
    tensao_aplicada_terciario = safe_str_output(transformer_data.get("teste_tensao_aplicada_terciario"))

    # Calcular valores padrão para Tensão Induzida (geralmente 2x a tensão nominal)
    def get_default_tensao_induzida(um_value):
        try:
            um_float = float(str(um_value).replace(",", ".").strip())
            # Tensão induzida geralmente é 2x a tensão nominal
            return f"{um_float * 2:.1f}"
        except:
            return "0"  # Valor padrão seguro

    # Obter valores de Tensão Induzida sem valores padrão
    tensao_induzida_at = safe_str_output(transformer_data.get("teste_tensao_induzida_at"))
    tensao_induzida_bt = safe_str_output(transformer_data.get("teste_tensao_induzida_bt"))
    tensao_induzida_terciario = safe_str_output(transformer_data.get("teste_tensao_induzida_terciario"))

    tipo_isolamento = transformer_data.get("tipo_isolamento", "uniforme")
    tipo_transformador = transformer_data.get("tipo_transformador")

    # Criar dados para o store
    data_to_store = {
        "parametros": {
            "enrolamentos": [],
            "tipo_transformador": tipo_transformador,
            "tipo_isolamento": tipo_isolamento,
        },
        "resultados": {"enrolamentos": []},
        "timestamp": time.time(),
    }

    # Adicionar enrolamentos
    if um_at:
        data_to_store["parametros"]["enrolamentos"].append(
            {
                "nome": "Alta Tensão",
                "um": um_at,
                "conexao": conexao_at,
                "neutro_um": neutro_um_at,
                "ia": nbi_at,
                "ia_neutro": nbi_neutro_at,
                "im": sil_at,
                "tensao_curta": tensao_aplicada_at,
                "nbi_neutro": nbi_neutro_at,
            }
        )
        resultados_enrolamento_at = {
            "nome": "Alta Tensão",
            "impulso_cortado": {"valor_str": iac_at},
        }
        data_to_store["resultados"]["enrolamentos"].append(resultados_enrolamento_at)

    if um_bt:
        data_to_store["parametros"]["enrolamentos"].append(
            {
                "nome": "Baixa Tensão",
                "um": um_bt,
                "conexao": conexao_bt,
                "neutro_um": neutro_um_bt,
                "ia": nbi_bt,
                "ia_neutro": nbi_neutro_bt,
                "im": sil_bt,
                "tensao_curta": tensao_aplicada_bt,
                "nbi_neutro": nbi_neutro_bt,
            }
        )
        resultados_enrolamento_bt = {
            "nome": "Baixa Tensão",
            "impulso_cortado": {"valor_str": iac_bt},
        }
        data_to_store["resultados"]["enrolamentos"].append(resultados_enrolamento_bt)

    if um_terciario:
        data_to_store["parametros"]["enrolamentos"].append(
            {
                "nome": "Terciário",
                "um": um_terciario,
                "conexao": conexao_terciario,
                "neutro_um": neutro_um_terciario,
                "ia": nbi_terciario,
                "ia_neutro": nbi_neutro_terciario,
                "im": sil_terciario,
                "tensao_curta": tensao_aplicada_terciario,
                "nbi_neutro": nbi_neutro_terciario,
            }
        )
        resultados_enrolamento_terciario = {
            "nome": "Terciário",
            "impulso_cortado": {"valor_str": iac_terciario},
        }
        data_to_store["resultados"]["enrolamentos"].append(resultados_enrolamento_terciario)

    # Adicionar informações de tensão induzida
    data_to_store["tensao_induzida"] = {
        "at": tensao_induzida_at,
        "bt": tensao_induzida_bt,
        "terciario": tensao_induzida_terciario,
    }

    # Log detalhado dos dados gerados
    log.info(
        f"[FORCE LOAD] Dados gerados para AT: Um={um_at}, Conexão={conexao_at}, NBI={nbi_at}, SIL={sil_at}, IAC={iac_at}"
    )
    log.info(
        f"[FORCE LOAD] Dados gerados para BT: Um={um_bt}, Conexão={conexao_bt}, NBI={nbi_bt}, SIL={sil_bt}, IAC={iac_bt}"
    )
    log.info(
        f"[FORCE LOAD] Dados gerados para Terciário: Um={um_terciario}, Conexão={conexao_terciario}, NBI={nbi_terciario}, SIL={sil_terciario}, IAC={iac_terciario}"
    )
    log.info(
        f"[FORCE LOAD] Tensões Aplicadas: AT={tensao_aplicada_at}, BT={tensao_aplicada_bt}, Terciário={tensao_aplicada_terciario}"
    )
    log.info(
        f"[FORCE LOAD] Tensões Induzidas: AT={tensao_induzida_at}, BT={tensao_induzida_bt}, Terciário={tensao_induzida_terciario}"
    )

    # Verificar se o state_manager está disponível
    from dash import callback_context

    try:
        if callback_context and hasattr(callback_context, 'app') and callback_context.app:
            app_instance = callback_context.app
            if hasattr(app_instance, 'state_manager') and app_instance.state_manager is not None:
                log.info("[FORCE LOAD] Usando state_manager para salvar dados dielétricos")
                print("[FORCE LOAD] Usando state_manager para salvar dados dielétricos")

                # Usar state_manager para salvar os dados
                app_instance.state_manager.set_store("dieletric-analysis-store", data_to_store)
                log.info("[FORCE LOAD] Dados dielétricos salvos via state_manager")
                print("[FORCE LOAD] Dados dielétricos salvos via state_manager")
            else:
                log.warning("[FORCE LOAD] state_manager não disponível no app")
                print("[FORCE LOAD] state_manager não disponível no app")
        else:
            log.warning("[FORCE LOAD] Não foi possível acessar o app do callback_context")
            print("[FORCE LOAD] Não foi possível acessar o app do callback_context")
    except Exception as e:
        log.warning(f"[FORCE LOAD] Erro ao salvar dados via state_manager: {e}")
        print(f"[FORCE LOAD] Erro ao salvar dados via state_manager: {e}")

    # Retornar os dados gerados
    return data_to_store


# --- Callback para exibir informações do transformador na página ---
# Este callback copia o conteúdo do painel global para o painel específico da página
# Removido callback duplicado que estava causando conflito


# --- Callback para exibir os parâmetros selecionados ---
@callback(
    Output("selected-params-display", "children"),
    Input("dieletric-analysis-store", "data"),
    prevent_initial_call=False,
)
def display_selected_parameters(dieletric_data):
    """
    Exibe os parâmetros selecionados na página de Análise Dielétrica.

    Args:
        dieletric_data: Dados da análise dielétrica

    Returns:
        children: Componentes HTML com os parâmetros selecionados
    """
    log.info("[DISPLAY SELECTED PARAMS] Exibindo parâmetros selecionados")

    if not dieletric_data or not isinstance(dieletric_data, dict):
        return html.Div(
            [
                html.P(
                    "Nenhum dado de análise dielétrica encontrado. Volte à página de Análise Dielétrica e preencha os campos.",
                    className="text-warning",
                )
            ]
        )

    try:
        enrolamentos = dieletric_data.get("parametros", {}).get("enrolamentos", [])
        if not enrolamentos:
            return html.P(
                "Nenhum enrolamento encontrado nos dados de análise dielétrica.",
                className="text-warning",
            )

        # Estilos para os cards de parâmetros
        card_style = {
            "backgroundColor": COLORS["background_card"],
            "borderRadius": "4px",
            "boxShadow": "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
            "marginBottom": "12px",
            "overflow": "hidden",
            "border": f"1px solid {COLORS['border']}",
        }

        header_style = {
            "backgroundColor": COLORS["background_card_header"],
            "color": COLORS["text_light"],
            "padding": "8px 12px",
            "fontWeight": "bold",
            "borderBottom": f"1px solid {COLORS['border']}",
            "fontSize": "0.9rem",
        }

        param_row_style = {
            "display": "flex",
            "flexWrap": "wrap",
            "padding": "6px 12px",
            "borderBottom": f"1px solid {COLORS['border']}",
            "backgroundColor": COLORS["background_card"],
        }

        param_label_style = {
            "fontWeight": "bold",
            "color": COLORS["text_muted"],
            "width": "120px",
            "fontSize": "0.8rem",
            "display": "inline-block",
            "marginRight": "8px",
        }

        param_value_style = {
            "color": COLORS["text_light"],
            "fontSize": "0.8rem",
            "fontWeight": "500",
        }

        # Organizar cards em colunas (AT, BT, Terciário)
        at_card = None
        bt_card = None
        terciario_card = None

        # Mapear enrolamentos por nome
        for enrol in enrolamentos:
            nome = enrol.get("nome", "")
            um = enrol.get("um", "")
            conexao = enrol.get("conexao", "")
            ia = enrol.get("ia", "")
            im = enrol.get("im", "")
            neutro_um = enrol.get("neutro_um", "")
            ia_neutro = enrol.get("ia_neutro", "")
            tensao_curta = enrol.get("tensao_curta", "")

            if nome and um:
                # Definir cor do cabeçalho baseado no tipo de enrolamento
                header_bg_color = (
                    COLORS["primary"]
                    if "Alta" in nome
                    else (COLORS["secondary"] if "Baixa" in nome else COLORS["info"])
                )

                # Parâmetros básicos
                params = [
                    {"label": "Classe (kV)", "value": um},
                    {"label": "Conexão", "value": conexao},
                    {"label": "BIL/NBI (kV)", "value": ia},
                    {
                        "label": "SIL/IM (kV)",
                        "value": im if im and im != "NA" and im != "0" else "N/A",
                    },
                    {
                        "label": "Tensão Aplicada",
                        "value": f"{tensao_curta} kV" if tensao_curta else "N/A",
                    },
                ]

                # Adicionar parâmetros de neutro se aplicável
                if conexao == "YN" and neutro_um:
                    params.extend(
                        [
                            {"label": "Um Neutro (kV)", "value": neutro_um},
                            {"label": "NBI Neutro (kV)", "value": ia_neutro},
                        ]
                    )

                # Criar linhas de parâmetros
                param_rows = []
                for param in params:
                    param_rows.append(
                        html.Div(
                            [
                                html.Span(f"{param['label']}:  ", style=param_label_style),
                                html.Span(param["value"], style=param_value_style),
                            ],
                            style=param_row_style,
                        )
                    )

                # Criar card para o enrolamento
                enrolamento_card = html.Div(
                    [
                        html.Div(nome, style={**header_style, "backgroundColor": header_bg_color}),
                        html.Div(param_rows, style={"padding": "4px 0"}),
                    ],
                    style={**card_style, "height": "100%"},
                )

                # Atribuir o card à coluna correta
                if "Alta" in nome:
                    at_card = enrolamento_card
                elif "Baixa" in nome:
                    bt_card = enrolamento_card
                elif "Terciário" in nome or "Terciario" in nome:
                    terciario_card = enrolamento_card

        # Criar placeholders para enrolamentos não encontrados
        if not at_card:
            at_card = html.Div(
                [
                    html.Div(
                        "Alta Tensão", style={**header_style, "backgroundColor": COLORS["primary"]}
                    ),
                    html.Div(
                        "Dados não disponíveis",
                        style={
                            "padding": "10px",
                            "textAlign": "center",
                            "color": COLORS["text_muted"],
                        },
                    ),
                ],
                style={**card_style, "height": "100%"},
            )

        if not bt_card:
            bt_card = html.Div(
                [
                    html.Div(
                        "Baixa Tensão",
                        style={**header_style, "backgroundColor": COLORS["secondary"]},
                    ),
                    html.Div(
                        "Dados não disponíveis",
                        style={
                            "padding": "10px",
                            "textAlign": "center",
                            "color": COLORS["text_muted"],
                        },
                    ),
                ],
                style={**card_style, "height": "100%"},
            )

        if not terciario_card:
            terciario_card = html.Div(
                [
                    html.Div(
                        "Terciário", style={**header_style, "backgroundColor": COLORS["info"]}
                    ),
                    html.Div(
                        "Dados não disponíveis",
                        style={
                            "padding": "10px",
                            "textAlign": "center",
                            "color": COLORS["text_muted"],
                        },
                    ),
                ],
                style={**card_style, "height": "100%"},
            )

        # Adicionar informações sobre o tipo de isolamento
        tipo_isolamento = dieletric_data.get("parametros", {}).get("tipo_isolamento", "")
        isolamento_info = None
        if tipo_isolamento:
            isolamento_info = html.Div(
                [
                    html.Div(
                        [
                            html.Span("Tipo de Isolamento:  ", style=param_label_style),
                            html.Span(tipo_isolamento.capitalize(), style=param_value_style),
                        ],
                        style={
                            "padding": "8px 12px",
                            "backgroundColor": COLORS["background_card_header"],
                            "borderRadius": "4px",
                        },
                    )
                ],
                style={"marginTop": "8px", "marginBottom": "4px"},
            )

        # Criar layout com 3 colunas
        return html.Div(
            [
                # Informações adicionais no topo
                isolamento_info if isolamento_info else html.Div(),
                # Layout de 3 colunas
                dbc.Row(
                    [
                        dbc.Col(at_card, width=4, className="px-1"),
                        dbc.Col(bt_card, width=4, className="px-1"),
                        dbc.Col(terciario_card, width=4, className="px-1"),
                    ],
                    className="g-2 mt-1",
                ),
            ]
        )

    except Exception as e:
        log.exception(f"Erro ao exibir parâmetros selecionados: {e}")
        return html.P(f"Erro ao exibir parâmetros selecionados: {str(e)}", className="text-danger")


# --- Callback para Análise Detalhada ---
@callback(
    [
        Output("comparison-output-at", "children"),
        Output("comparison-output-bt", "children"),
        Output("comparison-output-terciario", "children"),
        Output("comprehensive-analysis-store", "data"),
        Output("alert-container-comprehensive", "children"),
    ],
    Input("analisar-detalhes-button", "n_clicks"),
    State("dieletric-analysis-store", "data"),
    prevent_initial_call=True,
)
def analyze_dielectric_details(n_clicks, dieletric_data):
    """
    Realiza a análise detalhada comparativa entre NBR/IEC e IEEE para os parâmetros selecionados.

    Args:
        n_clicks: Número de cliques no botão
        dieletric_data: Dados da análise dielétrica

    Returns:
        comparison_output_at: Conteúdo para o div de comparação AT
        comparison_output_bt: Conteúdo para o div de comparação BT
        comparison_output_terciario: Conteúdo para o div de comparação Terciário
        data: Dados da análise para armazenamento
        alert: Mensagem de alerta (se houver)
    """
    if n_clicks is None or n_clicks == 0:
        raise PreventUpdate

    start_time = time.perf_counter()
    log.info("[ANALYZE DETAILS] Iniciando análise detalhada comparativa")

    # Adicionar logs detalhados para diagnóstico
    print("\n--- [ANALYZE DETAILS] Dados recebidos do dieletric-analysis-store ---")
    print(f"Tipo: {type(dieletric_data)}")
    if isinstance(dieletric_data, dict):
        print(f"Chaves: {list(dieletric_data.keys())}")
        # Imprimir sub-estrutura relevante
        params = dieletric_data.get("parametros", {})
        print(f"  parametros (tipo): {type(params).__name__}")
        if isinstance(params, dict):
            print(f"  parametros chaves: {list(params.keys())}")
            tipo_transformador = params.get("tipo_transformador")
            tipo_isolamento = params.get("tipo_isolamento")
            print(f"  tipo_transformador: {tipo_transformador}")
            print(f"  tipo_isolamento: {tipo_isolamento}")

            enrolamentos = params.get("enrolamentos", [])
            print(f"  parametros -> enrolamentos: {len(enrolamentos)} itens")
            for i, enrol in enumerate(enrolamentos):
                print(f"    Enrolamento #{i+1}: {enrol.get('nome', 'Sem nome')}")
                print(f"      Chaves: {list(enrol.keys())}")
                print(f"      Um: {enrol.get('um')}, Conexão: {enrol.get('conexao')}")
                print(f"      IA: {enrol.get('ia')}, IM: {enrol.get('im')}")

        resultados = dieletric_data.get("resultados", {})
        print(f"  resultados (tipo): {type(resultados).__name__}")
        if isinstance(resultados, dict):
            print(f"  resultados chaves: {list(resultados.keys())}")
            enrolamentos_res = resultados.get("enrolamentos", [])
            print(f"  resultados -> enrolamentos: {len(enrolamentos_res)} itens")
            for i, enrol in enumerate(enrolamentos_res):
                print(f"    Enrolamento #{i+1}: {enrol.get('nome', 'Sem nome')}")
                print(f"      Chaves: {list(enrol.keys())}")
    else:
        print(f"  Conteúdo: {dieletric_data}")
    print("--- Fim [ANALYZE DETAILS] ---")

    # Verificar se temos dados válidos
    if not dieletric_data or not isinstance(dieletric_data, dict):
        log.warning("[ANALYZE DETAILS] Dados inválidos ou vazios recebidos")
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            dbc.Alert(
                "Nenhum dado de análise dielétrica encontrado. Volte à página de Análise Dielétrica e preencha os campos.",
                color="warning",
                dismissable=True,
            ),
        )

    # Obter verificador
    verificador = get_verificador_instance()
    if verificador is None:
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            dbc.Alert(
                "Não foi possível inicializar o verificador de normas. Verifique os logs para mais detalhes.",
                color="danger",
                dismissable=True,
            ),
        )

    # Extrair dados dos enrolamentos
    try:
        enrolamentos = dieletric_data.get("parametros", {}).get("enrolamentos", [])
        tipo_transformador = dieletric_data.get("parametros", {}).get(
            "tipo_transformador", "Imerso em Óleo"
        )
        tipo_isolamento = dieletric_data.get("parametros", {}).get("tipo_isolamento", "uniforme")

        if not enrolamentos:
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                dbc.Alert(
                    "Nenhum enrolamento encontrado nos dados de análise dielétrica.",
                    color="warning",
                    dismissable=True,
                ),
            )

        # Mapear enrolamentos por nome
        enrolamentos_map = {enrol.get("nome", ""): enrol for enrol in enrolamentos}

        # Preparar resultados para cada enrolamento
        at_output = create_enrolamento_comparison(
            verificador, enrolamentos_map.get("Alta Tensão"), tipo_transformador, tipo_isolamento
        )
        bt_output = create_enrolamento_comparison(
            verificador, enrolamentos_map.get("Baixa Tensão"), tipo_transformador, tipo_isolamento
        )
        terciario_output = create_enrolamento_comparison(
            verificador, enrolamentos_map.get("Terciário"), tipo_transformador, tipo_isolamento
        )

        # Armazenar dados para uso posterior
        transformer_store_data = {
            "enrolamentos": [
                {"nome": "Alta Tensão", "dados": enrolamentos_map.get("Alta Tensão")},
                {"nome": "Baixa Tensão", "dados": enrolamentos_map.get("Baixa Tensão")},
                {"nome": "Terciário", "dados": enrolamentos_map.get("Terciário")},
            ],
            "tipo_transformador": tipo_transformador,
            "tipo_isolamento": tipo_isolamento,
            "timestamp": time.time(),
        }

        # Mensagem de sucesso
        alert = dbc.Alert(
            f"Análise comparativa concluída com sucesso em {time.perf_counter() - start_time:.2f} segundos.",
            color="success",
            dismissable=True,
            duration=4000,
        )

        return at_output, bt_output, terciario_output, transformer_store_data, alert

    except Exception as e:
        log.exception(f"[ANALYZE DETAILS] Erro ao analisar detalhes: {e}")
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            dbc.Alert(
                f"Erro ao realizar análise detalhada: {str(e)}", color="danger", dismissable=True
            ),
        )


def create_enrolamento_comparison(verificador, enrolamento_data, tipo_transformador, _):
    """
    Cria a comparação detalhada para um enrolamento específico.

    Args:
        verificador: Instância do VerificadorTransformador
        enrolamento_data: Dados do enrolamento
        tipo_transformador: Tipo do transformador
        _: Parâmetro não utilizado (tipo_isolamento)

    Returns:
        children: Componentes HTML com a comparação
    """
    if not enrolamento_data:
        return html.P("Dados não disponíveis para este enrolamento.", className="text-muted")

    nome = enrolamento_data.get("nome", "")
    um = enrolamento_data.get("um", "")
    conexao = enrolamento_data.get("conexao", "")
    ia = enrolamento_data.get("ia", "")
    im = enrolamento_data.get("im", "")
    tensao_curta = enrolamento_data.get("tensao_curta", "")
    neutro_um = enrolamento_data.get("neutro_um", "")
    ia_neutro = enrolamento_data.get("ia_neutro", "")

    if not um:
        return html.P(f"Classe de tensão (Um) não definida para {nome}.", className="text-warning")

    # Converter valores para float para cálculos
    um_float = safe_float(um)
    ia_float = safe_float(ia)

    # Obter dados NBR e IEEE
    try:
        # Obter dados NBR
        nbr_data = {
            "norma": "NBR/IEC",
            "um_kv": um,
            "bil_kvp": ia,
            "sil_kvp": im if im and im != "NA" else "N/A",
            "tensao_aplicada": tensao_curta,
            "conexao": conexao,
        }

        # Adicionar dados de neutro se aplicável
        if conexao == "YN" and neutro_um:
            nbr_data["neutro_um"] = neutro_um
            nbr_data["neutro_bil"] = ia_neutro

        # Obter dados IEEE
        ieee_data = verificador.ieee.get_test_levels(um) if hasattr(verificador, "ieee") else None
        if ieee_data:
            ieee_data["norma"] = "IEEE"
            ieee_data["um_kv"] = um
            ieee_data["conexao"] = conexao

            # Adicionar dados de neutro se aplicável
            if conexao == "YN" and neutro_um and hasattr(verificador, "ieee"):
                ieee_neutro_data = verificador.ieee.get_test_levels(neutro_um)
                if ieee_neutro_data:
                    ieee_data["neutro_um"] = neutro_um
                    ieee_data["neutro_bil"] = ieee_neutro_data.get("bil")

        # Obter distâncias dielétricas
        clearances = (
            verificador.get_clearances(um, tipo_transformador, ia, im)
            if hasattr(verificador, "get_clearances")
            else {}
        )
        nbr_clearances = clearances.get("NBR", {})
        ieee_clearances = clearances.get("IEEE", {})

        # Criar componentes de comparação
        components = []

        # 1. Tabela de Níveis de Isolamento
        components.append(html.H6("Níveis de Isolamento", className="mt-3 mb-2"))
        components.append(
            create_comparison_table(
                "Níveis de Isolamento",
                {
                    "Um (kV)": um,
                    "BIL/NBI (kVp)": ia,
                    "SIL/IM (kVp)": im if im and im != "NA" else "N/A",
                    "Conexão": conexao,
                },
                {
                    "Um (kV)": um,
                    "BIL/NBI (kVp)": ieee_data.get("bil", "N/A") if ieee_data else "N/A",
                    "SIL/IM (kVp)": ieee_data.get("sil", "N/A") if ieee_data else "N/A",
                    "Conexão": conexao,
                },
            )
        )

        # 2. Tabela de Ensaios de Tensão Aplicada
        components.append(html.H6("Ensaios de Tensão Aplicada", className="mt-3 mb-2"))
        components.append(
            create_comparison_table(
                "Tensão Aplicada",
                {"Tensão (kV rms)": tensao_curta, "Duração (s)": "60", "Frequência (Hz)": "50/60"},
                {
                    "Tensão (kV rms)": ieee_data.get("applied", "N/A") if ieee_data else "N/A",
                    "Duração (s)": "60",
                    "Frequência (Hz)": "60",
                },
            )
        )

        # 3. Tabela de Ensaios de Tensão Induzida
        components.append(html.H6("Ensaios de Tensão Induzida", className="mt-3 mb-2"))

        # Obter valores de tensão induzida
        nbr_acsd = "N/A"
        nbr_acld = "N/A"

        if hasattr(verificador, "nbr"):
            if hasattr(verificador.nbr, "get_tensao_curta_values"):
                nbr_acsd_values = verificador.nbr.get_tensao_curta_values(um, conexao, neutro_um)
                if nbr_acsd_values and len(nbr_acsd_values) > 0:
                    nbr_acsd = nbr_acsd_values[0]

            if hasattr(verificador.nbr, "get_tensao_longa_values"):
                nbr_acld_values = verificador.nbr.get_tensao_longa_values(um, conexao, neutro_um)
                if nbr_acld_values and len(nbr_acld_values) > 0:
                    nbr_acld = nbr_acld_values[0]

        components.append(
            create_comparison_table(
                "Tensão Induzida",
                {
                    "Curta Duração (kV)": nbr_acsd,
                    "Longa Duração (kV)": nbr_acld,
                    "Medição DP": "Sim" if um_float and um_float >= 72.5 else "Não",
                },
                {
                    "Curta Duração (kV)": ieee_data.get("acsd", "N/A") if ieee_data else "N/A",
                    "Longa Duração (kV)": ieee_data.get("acld", "N/A") if ieee_data else "N/A",
                    "Medição DP": "Sim" if um_float and um_float >= 72.5 else "Não",
                },
            )
        )

        # 4. Tabela de Ensaios de Impulso
        components.append(html.H6("Ensaios de Impulso", className="mt-3 mb-2"))
        components.append(
            create_comparison_table(
                "Impulso Atmosférico",
                {
                    "BIL/NBI (kVp)": ia,
                    "Impulso Cortado (kVp)": f"{float(ia) * 1.1:.1f}" if ia_float else "N/A",
                    "Forma de Onda": "1.2/50 µs",
                },
                {
                    "BIL/NBI (kVp)": ieee_data.get("bil", "N/A") if ieee_data else "N/A",
                    "Impulso Cortado (kVp)": f"{float(ieee_data.get('bil', 0)) * 1.15:.1f}"
                    if ieee_data and ieee_data.get("bil")
                    else "N/A",
                    "Forma de Onda": "1.2/50 µs",
                },
            )
        )

        # 5. Tabela de Distâncias Dielétricas
        components.append(html.H6("Distâncias Dielétricas", className="mt-3 mb-2"))
        components.append(
            create_comparison_table(
                "Distâncias Mínimas",
                {
                    "Fase-Terra (mm)": nbr_clearances.get("fase_terra", "N/A"),
                    "Fase-Fase (mm)": nbr_clearances.get("fase_fase", "N/A"),
                    "Entre Enrolamentos (mm)": nbr_clearances.get("outro_enrolamento", "N/A"),
                },
                {
                    "Fase-Terra (mm)": ieee_clearances.get("fase_terra", "N/A"),
                    "Fase-Fase (mm)": ieee_clearances.get("fase_fase", "N/A"),
                    "Entre Enrolamentos (mm)": ieee_clearances.get("outro_enrolamento", "N/A"),
                },
            )
        )

        # 6. Informações de Neutro (se aplicável)
        if conexao == "YN" and neutro_um:
            components.append(html.H6("Dados do Neutro", className="mt-3 mb-2"))

            # Obter valor de tensão aplicada para o neutro
            nbr_neutro_acsd = "N/A"
            if hasattr(verificador, "nbr") and hasattr(verificador.nbr, "get_tensao_curta_values"):
                nbr_neutro_acsd_values = verificador.nbr.get_tensao_curta_values(
                    neutro_um, "Y", None
                )
                if nbr_neutro_acsd_values and len(nbr_neutro_acsd_values) > 0:
                    nbr_neutro_acsd = nbr_neutro_acsd_values[0]

            components.append(
                create_comparison_table(
                    "Isolamento do Neutro",
                    {
                        "Um Neutro (kV)": neutro_um,
                        "BIL/NBI Neutro (kVp)": ia_neutro,
                        "Tensão Aplicada (kV)": nbr_neutro_acsd,
                    },
                    {
                        "Um Neutro (kV)": neutro_um,
                        "BIL/NBI Neutro (kVp)": ieee_data.get("neutro_bil", "N/A")
                        if ieee_data and "neutro_bil" in ieee_data
                        else "N/A",
                        "Tensão Aplicada (kV)": ieee_neutro_data.get("applied", "N/A")
                        if "ieee_neutro_data" in locals() and ieee_neutro_data
                        else "N/A",
                    },
                )
            )

        return html.Div(components)

    except Exception as e:
        log.exception(f"[CREATE COMPARISON] Erro ao criar comparação para {nome}: {e}")
        return html.Div(
            [
                html.P(f"Erro ao criar comparação para {nome}:", className="text-danger"),
                html.P(str(e), className="text-danger"),
            ]
        )


# --- Callback para exibir informações do transformador na página ---
# Este callback copia o conteúdo do painel global para o painel específico da página
@callback(
    Output("transformer-info-comprehensive-page", "children"),
    Input("transformer-info-comprehensive", "children"),
    prevent_initial_call=False,
)
def update_comprehensive_page_info_panel(global_panel_content):
    """Copia o conteúdo do painel global para o painel específico da página."""
    log.debug(
        "Atualizando painel de informações do transformador na página de análise dielétrica abrangente"
    )
    return global_panel_content


# End of file
