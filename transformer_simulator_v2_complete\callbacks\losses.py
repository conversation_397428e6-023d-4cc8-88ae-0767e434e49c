# callbacks/losses.py
import datetime
import itertools
import logging
import math

import dash
import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash import Input, Output, State, html, no_update, ctx
from dash.exceptions import PreventUpdate

from app import app
from config import (
    CARD_HEADER_STYLE,
    ERROR_STYLE,
    PLACEHOLDER_STYLE,
    TABLE_HEADER_STYLE_MD,
    TABLE_HEADER_STYLE_SM,
    TABLE_PARAM_STYLE_MD,
    TABLE_PARAM_STYLE_SM,
    TABLE_STATUS_STYLE,
    TABLE_VALUE_STYLE_MD,
    TABLE_VALUE_STYLE_SM,
    TABLE_WRAPPER_STYLE,
)
from config import colors as CONFIG_COLORS
from utils.constants import (
    CAPACITORS_BY_VOLTAGE,
    CS_SWITCHES_BY_VOLTAGE_MONO,
    CS_SWITCHES_BY_VOLTAGE_TRI,
    DUT_POWER_LIMIT,
    EPS_CURRENT_LIMIT,
    Q_SWITCH_POWERS,
    SUT_AT_MAX_VOLTAGE,
    SUT_AT_MIN_VOLTAGE,
    SUT_AT_STEP_VOLTAGE,
    SUT_BT_VOLTAGE,
    perdas_nucleo_data,
    potencia_magnet_data,
)

# Importar funções de utilidade para stores
from utils.store_diagnostics import convert_numpy_types
from utils.store_structure import ensure_store_structure, update_store_section

# Importar estilos do módulo centralizado
from utils.styles import COLORS, COMPONENTS, TYPOGRAPHY

# Importações da aplicação
from components.validators import validate_dict_inputs, safe_float

log = logging.getLogger(__name__)

# Data Definitions (No-Load Losses)
potencia_magnet = potencia_magnet_data
perdas_nucleo = perdas_nucleo_data

# Tolerance for floating point comparisons
epsilon = 1e-6

# DataFrame Creation (No-Load Losses)
try:
    df_potencia_magnet = pd.DataFrame(
        list(potencia_magnet.items()), columns=["key", "potencia_magnet"]
    )
    df_potencia_magnet[["inducao_nominal", "frequencia_nominal"]] = pd.DataFrame(
        df_potencia_magnet["key"].tolist(), index=df_potencia_magnet.index
    )
    df_potencia_magnet.drop("key", axis=1, inplace=True)
    df_potencia_magnet.set_index(["inducao_nominal", "frequencia_nominal"], inplace=True)

    df_perdas_nucleo = pd.DataFrame(list(perdas_nucleo.items()), columns=["key", "perdas_nucleo"])
    df_perdas_nucleo[["inducao_nominal", "frequencia_nominal"]] = pd.DataFrame(
        df_perdas_nucleo["key"].tolist(), index=df_perdas_nucleo.index
    )
    df_perdas_nucleo.drop("key", axis=1, inplace=True)
    df_perdas_nucleo.set_index(["inducao_nominal", "frequencia_nominal"], inplace=True)
except Exception as e:
    log.error(f"Erro criando DataFrames: {e}")
    df_potencia_magnet, df_perdas_nucleo = pd.DataFrame(), pd.DataFrame()


# Helpers
def create_input_row(label, id, placeholder, input_type="number"):
    """Creates a standard input row with label."""
    label_style = TYPOGRAPHY.get(
        "label", {"fontSize": "0.65rem", "fontWeight": "bold", "color": COLORS["text_dark"]}
    )
    input_base_style = COMPONENTS.get(
        "input",
        {
            "fontSize": "0.7rem",
            "color": COLORS["text_dark"],
            "backgroundColor": COLORS["background_input"],
            "border": f"1px solid {COLORS["border"]}",
        },
    )
    final_input_style = {
        **input_base_style,
        "height": "26px",
        "padding": "0.15rem 0.3rem",
        "width": "75%",
    }
    return dbc.Row(
        [
            dbc.Col(dbc.Label(label, style=label_style), width=9, className="text-end pe-1"),
            dbc.Col(
                dbc.Input(
                    type=input_type,
                    id=id,
                    placeholder=placeholder,
                    persistence=True,
                    persistence_type="local",
                    style=final_input_style,
                ),
                width=3,
            ),
        ],
        className="g-1 mb-1",
    )


# --- Render Functions (Assumed to be in layouts/losses.py) ---
# Import render functions locally to avoid circular dependency
try:
    from layouts.losses import render_perdas_carga, render_perdas_vazio
except ImportError:
    log.error("Could not import render functions from layouts.losses. Defining placeholders.")

    def render_perdas_vazio():
        return html.Div("Layout Vazio não carregado.", style=ERROR_STYLE)

    def render_perdas_carga():
        return html.Div("Layout Carga não carregado.", style=ERROR_STYLE)


# --- Callbacks ---
@dash.callback(
    Output("conteudo-perdas", "children"),
    [
        Input("tabs-perdas", "active_tab"),
        # Removido Input("losses-store", "data") para evitar re-renderização quando o store muda
    ]
)
def losses_render_tab_content(tab_ativa):
    """
    Renderiza o conteúdo da aba selecionada na página de perdas.
    Agora é acionado APENAS quando a aba é alterada, não quando o store muda.
    """
    log.debug(f"losses_render_tab_content triggered. Active tab: {tab_ativa}, Trigger: {ctx.triggered_id}")

    if tab_ativa == "tab-vazio":
        return render_perdas_vazio()
    elif tab_ativa == "tab-carga":
        return render_perdas_carga()
    return html.P("Selecione uma aba.")


# --- Callbacks para Carregar Valores do Store (Perdas Vazio e Carga) ---
@dash.callback(
    [
        Output("perdas-vazio-kw", "value"),  # Removido allow_duplicate
        Output("peso-projeto-Ton", "value"),
        Output("corrente-excitacao", "value"),
        Output("inducao-nucleo", "value"),
        Output("corrente-excitacao-1-1", "value"),
        Output("corrente-excitacao-1-2", "value"),
    ],
    [
        Input("tabs-perdas", "active_tab"),
        Input("losses-store", "data")  # Adicionado Input do losses-store
    ],
    # Removido prevent_initial_call=True para garantir que o callback seja executado na carga inicial
)
def losses_populate_vazio_inputs(active_tab, losses_data_from_store):
    """
    Carrega e popula os valores de perdas em vazio nos inputs quando a aba é selecionada
    ou quando o store losses-store é atualizado.
    """
    log.critical(f"[LOSSES POPULATE VAZIO] Acionado. Aba: {active_tab}, Trigger: {ctx.triggered_id if ctx.triggered else 'N/A'}")

    if active_tab != "tab-vazio":
        # Se a aba não é a de vazio, não atualizamos NADA (nem com None).
        # Isso evita que os valores sejam apagados ao mudar de aba e voltar.
        raise PreventUpdate

    # Tentar usar os dados do store primeiro
    losses_data = losses_data_from_store

    # Se o store estiver vazio, tentar carregar do state_manager ou MCP diretamente
    if not losses_data or not isinstance(losses_data, dict) or "resultados_perdas_vazio" not in losses_data:
        log.warning("[LOSSES POPULATE VAZIO] Store vazio ou sem dados de vazio. Tentando carregar do state_manager...")
        if hasattr(app, "state_manager") and app.state_manager is not None:
            store_data = app.state_manager.get_store("losses-store")
            if store_data and isinstance(store_data, dict) and "resultados_perdas_vazio" in store_data:
                log.info("[LOSSES POPULATE VAZIO] Dados encontrados no state_manager. Usando-os para popular os inputs.")
                losses_data = store_data
            else:
                log.warning("[LOSSES POPULATE VAZIO] Nenhum dado encontrado no state_manager. Tentando MCP...")
                if hasattr(app, "mcp") and app.mcp is not None:
                    mcp_data = app.mcp.get_data("losses-store")
                    if mcp_data and isinstance(mcp_data, dict) and "resultados_perdas_vazio" in mcp_data:
                        log.info("[LOSSES POPULATE VAZIO] Dados encontrados no MCP. Usando-os para popular os inputs.")
                        losses_data = mcp_data
                    else:
                        log.warning("[LOSSES POPULATE VAZIO] Nenhum dado encontrado no MCP.")
        elif hasattr(app, "mcp") and app.mcp is not None:
            mcp_data = app.mcp.get_data("losses-store")
            if mcp_data and isinstance(mcp_data, dict) and "resultados_perdas_vazio" in mcp_data:
                log.info("[LOSSES POPULATE VAZIO] Dados encontrados no MCP. Usando-os para popular os inputs.")
                losses_data = mcp_data
            else:
                log.warning("[LOSSES POPULATE VAZIO] Nenhum dado encontrado no MCP.")

    # Lista de chaves a serem carregadas
    keys_to_load = [
        "perdas_vazio_kw",
        "peso_nucleo",
        "corrente_excitacao",
        "inducao",
        "corrente_exc_1_1",
        "corrente_exc_1_2",
    ]

    # Sempre retorna valores (None se não encontrado), não no_update.
    outputs = [None] * len(keys_to_load)

    if losses_data and isinstance(losses_data, dict):
        # Verificar múltiplas estruturas possíveis para compatibilidade
        if "resultados_perdas_vazio" in losses_data:
            stored = losses_data["resultados_perdas_vazio"]
            if isinstance(stored, dict):
                outputs = [
                    stored.get(k) for k in keys_to_load
                ]
                log.info(f"[LOSSES POPULATE VAZIO] Valores encontrados em 'resultados_perdas_vazio': {outputs}")
            else:
                log.warning("[LOSSES POPULATE VAZIO] 'resultados_perdas_vazio' não é um dicionário.")
        # Verificar se os dados estão diretamente no dicionário principal
        elif any(k in losses_data for k in keys_to_load):
            outputs = [
                losses_data.get(k) for k in keys_to_load
            ]
            log.info(f"[LOSSES POPULATE VAZIO] Valores encontrados diretamente no dicionário principal: {outputs}")
        else:
            log.warning("[LOSSES POPULATE VAZIO] Nenhum dado válido encontrado nas estruturas conhecidas.")
    else:
        log.warning("[LOSSES POPULATE VAZIO] Nenhum dado válido em 'losses-store' ou MCP para perdas em vazio. Campos de Vazio serão None.")

    return tuple(outputs)


@dash.callback(
    [
        Output("perdas-carga-kw_U_nom", "value"),  # Removido allow_duplicate
        Output("perdas-carga-kw_U_min", "value"),
        Output("perdas-carga-kw_U_max", "value"),
        Output("temperatura-referencia", "value"),
    ],
    [
        Input("tabs-perdas", "active_tab"),
        Input("losses-store", "data")  # Adicionado Input do losses-store
    ],
    # Removido prevent_initial_call=True para garantir que o callback seja executado na carga inicial
)
def losses_populate_carga_inputs(active_tab, losses_data_from_store):
    """
    Carrega e popula os valores de perdas em carga nos inputs quando a aba é selecionada
    ou quando o store losses-store é atualizado.
    """
    log.critical(f"[LOSSES POPULATE CARGA] Acionado. Aba: {active_tab}, Trigger: {ctx.triggered_id if ctx.triggered else 'N/A'}")

    if active_tab != "tab-carga":
        raise PreventUpdate

    # Tentar usar os dados do store primeiro
    losses_data = losses_data_from_store

    # Se o store estiver vazio, tentar carregar do state_manager ou MCP diretamente
    if not losses_data or not isinstance(losses_data, dict) or "resultados_perdas_carga" not in losses_data:
        log.warning("[LOSSES POPULATE CARGA] Store vazio ou sem dados de carga. Tentando carregar do state_manager...")
        if hasattr(app, "state_manager") and app.state_manager is not None:
            store_data = app.state_manager.get_store("losses-store")
            if store_data and isinstance(store_data, dict) and "resultados_perdas_carga" in store_data:
                log.info("[LOSSES POPULATE CARGA] Dados encontrados no state_manager. Usando-os para popular os inputs.")
                losses_data = store_data
            else:
                log.warning("[LOSSES POPULATE CARGA] Nenhum dado encontrado no state_manager. Tentando MCP...")
                if hasattr(app, "mcp") and app.mcp is not None:
                    mcp_data = app.mcp.get_data("losses-store")
                    if mcp_data and isinstance(mcp_data, dict) and "resultados_perdas_carga" in mcp_data:
                        log.info("[LOSSES POPULATE CARGA] Dados encontrados no MCP. Usando-os para popular os inputs.")
                        losses_data = mcp_data
                    else:
                        log.warning("[LOSSES POPULATE CARGA] Nenhum dado encontrado no MCP.")
        elif hasattr(app, "mcp") and app.mcp is not None:
            mcp_data = app.mcp.get_data("losses-store")
            if mcp_data and isinstance(mcp_data, dict) and "resultados_perdas_carga" in mcp_data:
                log.info("[LOSSES POPULATE CARGA] Dados encontrados no MCP. Usando-os para popular os inputs.")
                losses_data = mcp_data
            else:
                log.warning("[LOSSES POPULATE CARGA] Nenhum dado encontrado no MCP.")

    # Lista de chaves a serem carregadas
    keys_to_load = [
        "perdas_carga_nom",
        "perdas_carga_min",
        "perdas_carga_max",
        "temperatura_referencia",
    ]

    # Sempre retorna valores (None se não encontrado), não no_update.
    outputs = [None] * len(keys_to_load)

    if losses_data and isinstance(losses_data, dict):
        # Verificar múltiplas estruturas possíveis para compatibilidade
        if "resultados_perdas_carga" in losses_data:
            stored = losses_data["resultados_perdas_carga"]
            if isinstance(stored, dict):
                outputs = [
                    stored.get(k) for k in keys_to_load
                ]
                log.info(f"[LOSSES POPULATE CARGA] Valores encontrados em 'resultados_perdas_carga': {outputs}")
            else:
                log.warning("[LOSSES POPULATE CARGA] 'resultados_perdas_carga' não é um dicionário.")
        # Verificar se os dados estão diretamente no dicionário principal
        elif any(k in losses_data for k in keys_to_load):
            outputs = [
                losses_data.get(k) for k in keys_to_load
            ]
            log.info(f"[LOSSES POPULATE CARGA] Valores encontrados diretamente no dicionário principal: {outputs}")
        else:
            log.warning("[LOSSES POPULATE CARGA] Nenhum dado válido encontrado nas estruturas conhecidas.")
    else:
        log.warning("[LOSSES POPULATE CARGA] Nenhum dado válido em 'losses-store' ou MCP para perdas em carga. Campos de Carga serão None.")

    return tuple(outputs)


# --- Callback para Atualizar Cache de Dados do Transformador removido ---
# Este callback foi removido pois a atualização é feita pelo callback global em global_updates.py


# --- Callback para atualizar o painel de informações do transformador na página de perdas ---
@dash.callback(
    Output("transformer-info-losses-page", "children"),
    Input("transformer-info-losses", "children"),
    prevent_initial_call=False,
)
def update_losses_page_info_panel(global_panel_content):
    """
    Copia o conteúdo do painel de informações global para o painel local da página de perdas.
    Este callback é acionado quando o painel global é atualizado pelo callback global_updates_all_transformer_info_panels.
    """
    log.debug("Atualizando painel de informações do transformador na página de perdas")
    return global_panel_content


# --- Callback Perdas em Vazio (Atualizado para nova estrutura) ---
@dash.callback(
    [
        Output("parametros-gerais-card-body", "children"),
        Output("dut-voltage-level-results-body", "children"),
        Output("sut-analysis-results-area", "children"),
        Output("legend-observations-area", "children"),
        Output("losses-store", "data", allow_duplicate=True),  # Mantido para compatibilidade
    ],
    [Input("calcular-perdas-vazio", "n_clicks")],  # Input direto para trigger de usuário
    [
        State(id, "value")
        for id in [
            "perdas-vazio-kw",
            "peso-projeto-Ton",
            "corrente-excitacao",
            "inducao-nucleo",
            "corrente-excitacao-1-1",
            "corrente-excitacao-1-2",
        ]
    ] + [
        State("losses-store", "data"),  # State para dados necessários para cálculo
        State("transformer-inputs-store", "data"),  # State para dados básicos do transformador
    ],
    prevent_initial_call=True,
)
def calculate_no_load_losses(
    n_clicks,
    perdas_vazio_kw,
    peso_nucleo_ton,
    corrente_excitacao,
    inducao_nucleo,
    corrente_exc_1_1,
    corrente_exc_1_2,
    current_losses_data,
    transformer_data,
):
    """Calcula as perdas em vazio e atualiza os outputs e o store."""
    log.debug(f"Callback calculate_no_load_losses triggered by n_clicks={n_clicks}")

    if n_clicks is None or n_clicks == 0:
        raise PreventUpdate

    # Garantir que os dados do transformador estão disponíveis
    if not transformer_data or not isinstance(transformer_data, dict):
        log.error("Dados do transformador não encontrados no store.")
        error_msg = html.Div("Erro: Dados do transformador não disponíveis.", style=ERROR_STYLE)
        return error_msg, error_msg, error_msg, error_msg, no_update

    # Verificar se os dados estão aninhados
    if "transformer_data" in transformer_data and isinstance(transformer_data["transformer_data"], dict):
        transformer_dict = transformer_data["transformer_data"]
    else:
        transformer_dict = transformer_data

    # Validar inputs numéricos
    inputs_vazio = {
        "perdas_vazio_kw": perdas_vazio_kw,
        "peso_nucleo_ton": peso_nucleo_ton,
        "corrente_excitacao": corrente_excitacao,
        "inducao_nucleo": inducao_nucleo,
        "corrente_exc_1_1": corrente_exc_1_1,
        "corrente_exc_1_2": corrente_exc_1_2,
    }
    validated_inputs, error_messages = validate_dict_inputs(inputs_vazio)
    if error_messages:
        log.error(f"Erro de validação nos inputs de perdas em vazio: {error_messages}")
        error_display = html.Div([html.P(msg, style=ERROR_STYLE) for msg in error_messages])
        return error_display, error_display, error_display, error_display, no_update

    # Extrair valores validados
    perdas_vazio_kw = validated_inputs["perdas_vazio_kw"]
    peso_nucleo_ton = validated_inputs["peso_nucleo_ton"]
    corrente_excitacao = validated_inputs["corrente_excitacao"]
    inducao_nucleo = validated_inputs["inducao_nucleo"]
    corrente_exc_1_1 = validated_inputs["corrente_exc_1_1"]
    corrente_exc_1_2 = validated_inputs["corrente_exc_1_2"]

    # Extrair dados do transformador
    tensao_at = safe_float(transformer_dict.get("tensao_at"))
    frequencia = safe_float(transformer_dict.get("frequencia"))
    peso_parte_ativa = safe_float(transformer_dict.get("peso_parte_ativa"))

    if not all([tensao_at, frequencia, peso_parte_ativa]):
        log.error("Dados essenciais do transformador (tensão AT, frequência, peso parte ativa) estão faltando ou são inválidos.")
        error_msg = html.Div("Erro: Dados essenciais do transformador faltando.", style=ERROR_STYLE)
        return error_msg, error_msg, error_msg, error_msg, no_update

    try:
        # --- Cálculos --- (Adaptado da lógica original)
        peso_nucleo_kg = peso_nucleo_ton * 1000
        perda_nucleo_especifica = (perdas_vazio_kw * 1000) / peso_nucleo_kg if peso_nucleo_kg > 0 else 0

        # Interpolação para Potência Aparente Específica (Sn)
        if df_potencia_magnet.empty:
            raise ValueError("DataFrame de potência magnética não carregado.")

        inducao_vals = sorted(df_potencia_magnet.index.get_level_values("inducao_nominal").unique())
        freq_vals = sorted(df_potencia_magnet.index.get_level_values("frequencia_nominal").unique())

        # Garantir que inducao_nucleo e frequencia estão dentro dos limites
        inducao_nucleo = max(min(inducao_nucleo, max(inducao_vals)), min(inducao_vals))
        frequencia = max(min(frequencia, max(freq_vals)), min(freq_vals))

        i1 = max([i for i in inducao_vals if i <= inducao_nucleo])
        i2 = min([i for i in inducao_vals if i >= inducao_nucleo])
        f1 = max([f for f in freq_vals if f <= frequencia])
        f2 = min([f for f in freq_vals if f >= frequencia])

        Q11 = df_potencia_magnet.loc[(i1, f1), "potencia_magnet"]
        Q12 = df_potencia_magnet.loc[(i1, f2), "potencia_magnet"]
        Q21 = df_potencia_magnet.loc[(i2, f1), "potencia_magnet"]
        Q22 = df_potencia_magnet.loc[(i2, f2), "potencia_magnet"]

        if abs(i2 - i1) < epsilon and abs(f2 - f1) < epsilon:
            potencia_aparente_especifica = Q11
        elif abs(i2 - i1) < epsilon:
            potencia_aparente_especifica = Q11 + (Q12 - Q11) * (frequencia - f1) / (f2 - f1)
        elif abs(f2 - f1) < epsilon:
            potencia_aparente_especifica = Q11 + (Q21 - Q11) * (inducao_nucleo - i1) / (i2 - i1)
        else:
            R1 = Q11 + (Q21 - Q11) * (inducao_nucleo - i1) / (i2 - i1)
            R2 = Q12 + (Q22 - Q12) * (inducao_nucleo - i1) / (i2 - i1)
            potencia_aparente_especifica = R1 + (R2 - R1) * (frequencia - f1) / (f2 - f1)

        # Cálculos DUT
        tensao_dut = tensao_at * 1000 # kV para V
        potencia_aparente_nucleo = potencia_aparente_especifica * peso_nucleo_kg
        potencia_ativa_nucleo = perda_nucleo_especifica * peso_nucleo_kg

        if potencia_aparente_nucleo**2 < potencia_ativa_nucleo**2:
            log.warning(f"Potência aparente ({potencia_aparente_nucleo}) ao quadrado menor que potência ativa ({potencia_ativa_nucleo}) ao quadrado. Ajustando reativa para 0.")
            potencia_reativa_nucleo = 0
        else:
            potencia_reativa_nucleo = math.sqrt(potencia_aparente_nucleo**2 - potencia_ativa_nucleo**2)

        corrente_ativa = potencia_ativa_nucleo / tensao_dut if tensao_dut > 0 else 0
        corrente_reativa = potencia_reativa_nucleo / tensao_dut if tensao_dut > 0 else 0
        corrente_total = math.sqrt(corrente_ativa**2 + corrente_reativa**2)

        # Cálculos SUT
        sut_voltage_levels = np.arange(SUT_AT_MIN_VOLTAGE, SUT_AT_MAX_VOLTAGE + SUT_AT_STEP_VOLTAGE, SUT_AT_STEP_VOLTAGE)
        sut_results = []
        for sut_voltage_kv in sut_voltage_levels:
            sut_voltage = sut_voltage_kv * 1000
            # ... (cálculos SUT complexos omitidos para brevidade, assumindo que geram P, Q, S, I para cada nível)
            # Placeholder para resultados SUT
            p_sut = potencia_ativa_nucleo * (sut_voltage / tensao_dut)**2 # Simplificação
            q_sut = potencia_reativa_nucleo * (sut_voltage / tensao_dut)**2 # Simplificação
            s_sut = math.sqrt(p_sut**2 + q_sut**2)
            i_sut = s_sut / sut_voltage if sut_voltage > 0 else 0
            sut_results.append({
                "Tensao_kV": sut_voltage_kv,
                "P_kW": p_sut / 1000,
                "Q_kVAr": q_sut / 1000,
                "S_kVA": s_sut / 1000,
                "I_A": i_sut
            })

        df_sut = pd.DataFrame(sut_results)

        # --- Geração dos Resultados --- (Conteúdo para os cards)
        # 1. Parâmetros Gerais
        params_gerais_content = html.Div(
            [
                dbc.Row([dbc.Col("Perdas Vazio (kW)"), dbc.Col(f"{perdas_vazio_kw:.2f}")]),
                dbc.Row([dbc.Col("Peso Núcleo (Ton)"), dbc.Col(f"{peso_nucleo_ton:.2f}")]),
                dbc.Row([dbc.Col("Corrente Exc. (%)"), dbc.Col(f"{corrente_excitacao:.2f}")]),
                dbc.Row([dbc.Col("Indução Núcleo (T)"), dbc.Col(f"{inducao_nucleo:.2f}")]),
                dbc.Row([dbc.Col("Corrente Exc. 1.1 (%)"), dbc.Col(f"{corrente_exc_1_1:.2f}")]),
                dbc.Row([dbc.Col("Corrente Exc. 1.2 (%)"), dbc.Col(f"{corrente_exc_1_2:.2f}")]),
                dbc.Row([dbc.Col("Perda Específica (W/kg)"), dbc.Col(f"{perda_nucleo_especifica:.3f}")]),
                dbc.Row([dbc.Col("Pot. Ap. Específica (VA/kg)"), dbc.Col(f"{potencia_aparente_especifica:.3f}")]),
            ],
            className="text-dark small", # Garante texto escuro e pequeno
        )

        # 2. Nível de Tensão DUT
        dut_voltage_content = html.Div(
            [
                dbc.Row([dbc.Col("Tensão DUT (kV)"), dbc.Col(f"{tensao_at:.2f}")]),
                dbc.Row([dbc.Col("Pot. Ativa Núcleo (kW)"), dbc.Col(f"{potencia_ativa_nucleo / 1000:.2f}")]),
                dbc.Row([dbc.Col("Pot. Reativa Núcleo (kVAr)"), dbc.Col(f"{potencia_reativa_nucleo / 1000:.2f}")]),
                dbc.Row([dbc.Col("Pot. Aparente Núcleo (kVA)"), dbc.Col(f"{potencia_aparente_nucleo / 1000:.2f}")]),
                dbc.Row([dbc.Col("Corrente Ativa (A)"), dbc.Col(f"{corrente_ativa:.3f}")]),
                dbc.Row([dbc.Col("Corrente Reativa (A)"), dbc.Col(f"{corrente_reativa:.3f}")]),
                dbc.Row([dbc.Col("Corrente Total (A)"), dbc.Col(f"{corrente_total:.3f}")]),
            ],
            className="text-dark small", # Garante texto escuro e pequeno
        )

        # 3. Análise SUT (Tabela)
        sut_analysis_content = dbc.Table.from_dataframe(
            df_sut, striped=True, bordered=True, hover=True, size="sm"
        )

        # 4. Legenda e Observações
        legend_observations_content = html.Div(
            [
                html.H6("Legenda", className="mt-3 text-dark"),
                html.P("P: Potência Ativa, Q: Potência Reativa, S: Potência Aparente, I: Corrente", className="small text-muted"),
                html.H6("Observações", className="mt-3 text-dark"),
                html.P("Cálculos baseados nos dados de entrada e interpolação.", className="small text-muted"),
            ],
            className="text-dark", # Garante texto escuro
        )

        # --- Atualização do Store --- (Estrutura simplificada)
        resultados_vazio = {
            "perdas_vazio_kw": perdas_vazio_kw,
            "peso_nucleo": peso_nucleo_ton, # Renomeado para consistência?
            "corrente_excitacao": corrente_excitacao,
            "inducao": inducao_nucleo, # Renomeado para consistência?
            "corrente_exc_1_1": corrente_exc_1_1,
            "corrente_exc_1_2": corrente_exc_1_2,
            "perda_nucleo_especifica": perda_nucleo_especifica,
            "potencia_aparente_especifica": potencia_aparente_especifica,
            "potencia_ativa_nucleo_kw": potencia_ativa_nucleo / 1000,
            "potencia_reativa_nucleo_kvar": potencia_reativa_nucleo / 1000,
            "potencia_aparente_nucleo_kva": potencia_aparente_nucleo / 1000,
            "corrente_total_a": corrente_total,
            "df_sut_results": df_sut.to_dict("records"), # Salvar resultados SUT
            "last_calculated": datetime.datetime.now().isoformat(),
        }

        # Garante a estrutura do store antes de atualizar
        new_losses_data = ensure_store_structure(current_losses_data, ["resultados_perdas_vazio", "resultados_perdas_carga"])
        # Atualiza a seção específica
        new_losses_data = update_store_section(new_losses_data, "resultados_perdas_vazio", resultados_vazio)

        log.debug("Cálculo de perdas em vazio concluído com sucesso.")
        return (
            params_gerais_content,
            dut_voltage_content,
            sut_analysis_content,
            legend_observations_content,
            new_losses_data,
        )

    except ValueError as ve:
        log.error(f"Erro de valor durante cálculo de perdas em vazio: {ve}")
        error_msg = html.Div(f"Erro: {ve}", style=ERROR_STYLE)
        return error_msg, error_msg, error_msg, error_msg, no_update
    except Exception as e:
        log.exception("Erro inesperado durante cálculo de perdas em vazio:")
        error_msg = html.Div(f"Erro inesperado: {e}", style=ERROR_STYLE)
        return error_msg, error_msg, error_msg, error_msg, no_update


# --- Callback Perdas em Carga ---
@dash.callback(
    [
        Output("resultados-perdas-carga-container", "children"),
        Output("losses-store", "data", allow_duplicate=True),
    ],
    [Input("calcular-perdas-carga", "n_clicks")],
    [
        State("perdas-carga-kw_U_nom", "value"),
        State("perdas-carga-kw_U_min", "value"),
        State("perdas-carga-kw_U_max", "value"),
        State("temperatura-referencia", "value"),
        State("losses-store", "data"),
        State("transformer-inputs-store", "data"),
    ],
    prevent_initial_call=True,
)
def calculate_load_losses(
    n_clicks,
    perdas_carga_nom,
    perdas_carga_min,
    perdas_carga_max,
    temp_ref,
    current_losses_data,
    transformer_data,
):
    """Calcula as perdas em carga e atualiza o output e o store."""
    log.debug(f"Callback calculate_load_losses triggered by n_clicks={n_clicks}")

    if n_clicks is None or n_clicks == 0:
        raise PreventUpdate

    # Garantir que os dados do transformador estão disponíveis
    if not transformer_data or not isinstance(transformer_data, dict):
        log.error("Dados do transformador não encontrados no store.")
        return html.Div("Erro: Dados do transformador não disponíveis.", style=ERROR_STYLE), no_update

    # Verificar se os dados estão aninhados
    if "transformer_data" in transformer_data and isinstance(transformer_data["transformer_data"], dict):
        transformer_dict = transformer_data["transformer_data"]
    else:
        transformer_dict = transformer_data

    # Validar inputs numéricos
    inputs_carga = {
        "perdas_carga_nom": perdas_carga_nom,
        "perdas_carga_min": perdas_carga_min,
        "perdas_carga_max": perdas_carga_max,
        "temp_ref": temp_ref,
    }
    validated_inputs, error_messages = validate_dict_inputs(inputs_carga)
    if error_messages:
        log.error(f"Erro de validação nos inputs de perdas em carga: {error_messages}")
        error_display = html.Div([html.P(msg, style=ERROR_STYLE) for msg in error_messages])
        return error_display, no_update

    # Extrair valores validados
    perdas_carga_nom = validated_inputs["perdas_carga_nom"]
    perdas_carga_min = validated_inputs["perdas_carga_min"]
    perdas_carga_max = validated_inputs["perdas_carga_max"]
    temp_ref = validated_inputs["temp_ref"]

    # Extrair dados do transformador necessários
    # Exemplo: potencia_nominal = safe_float(transformer_dict.get("potencia_nominal"))
    # Adicione outras variáveis necessárias aqui

    try:
        # --- Cálculos --- (Lógica de cálculo de perdas em carga)
        # Placeholder para lógica de cálculo
        # Exemplo: perdas_corrigidas = perdas_carga_nom * fator_correcao(temp_ref)
        observations = [
            f"Perdas Nominais: {perdas_carga_nom} kW",
            f"Perdas Mínimas: {perdas_carga_min} kW",
            f"Perdas Máximas: {perdas_carga_max} kW",
            f"Temperatura de Referência: {temp_ref} °C",
            "Cálculos adicionais podem ser implementados aqui."
        ]
        results_dict = {
            "Perdas Nominais (kW)": perdas_carga_nom,
            "Perdas Mínimas (kW)": perdas_carga_min,
            "Perdas Máximas (kW)": perdas_carga_max,
            "Temp. Referência (°C)": temp_ref,
        }
        df_results = pd.DataFrame(list(results_dict.items()), columns=["Parâmetro", "Valor"])

        # --- Geração dos Resultados --- (Card com tema claro)
        results_content = dbc.Card(
            dbc.CardBody(
                [
                    html.H5("Resultados - Perdas em Carga", className="card-title text-dark"), # Added text-dark
                    dbc.Table.from_dataframe(
                        df_results, striped=True, bordered=True, hover=True, size="sm"
                    ), # Table content should inherit or be styled separately if needed
                    html.Hr(),
                    html.H6("Observações", className="mt-3 text-dark"), # Added text-dark
                    html.Ul([html.Li(obs, className="text-dark") for obs in observations]), # Added text-dark to list items
                ],
                className="text-dark" # Ensure body text is dark by default
            ),
            className="mt-3",
            color="light", # Added color="light"
        )

        # --- Atualização do Store --- (Estrutura simplificada)
        resultados_carga = {
            "perdas_carga_nom": perdas_carga_nom,
            "perdas_carga_min": perdas_carga_min,
            "perdas_carga_max": perdas_carga_max,
            "temperatura_referencia": temp_ref,
            "last_calculated": datetime.datetime.now().isoformat(),
        }

        # Garante a estrutura do store antes de atualizar
        new_losses_data = ensure_store_structure(current_losses_data, ["resultados_perdas_vazio", "resultados_perdas_carga"])
        # Atualiza a seção específica
        new_losses_data = update_store_section(new_losses_data, "resultados_perdas_carga", resultados_carga)

        log.debug("Cálculo de perdas em carga concluído com sucesso.")
        return results_content, new_losses_data

    except ValueError as ve:
        log.error(f"Erro de valor durante cálculo de perdas em carga: {ve}")
        return html.Div(f"Erro: {ve}", style=ERROR_STYLE), no_update
    except Exception as e:
        log.exception("Erro inesperado durante cálculo de perdas em carga:")
        return html.Div(f"Erro inesperado: {e}", style=ERROR_STYLE), no_update

log.debug("Callbacks de Perdas registrados.")

