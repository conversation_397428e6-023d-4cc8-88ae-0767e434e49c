# callbacks/global_updates.py
"""
Callbacks globais para atualização de componentes em múltiplas seções.
"""
import logging
import time

import dash
from dash import Input, Output, callback, ctx, html

from app import app  # Import app instance to access state_manager
from components.transformer_info_template import create_transformer_info_panel

log = logging.getLogger(__name__)

# Lista de ids dos painéis de informação
info_panel_outputs = [
    Output("transformer-info-losses", "children", allow_duplicate=True),
    Output("transformer-info-impulse", "children", allow_duplicate=True),
    Output("transformer-info-dieletric", "children", allow_duplicate=True),
    Output("transformer-info-applied", "children", allow_duplicate=True),
    Output("transformer-info-induced", "children", allow_duplicate=True),
    Output("transformer-info-short-circuit", "children", allow_duplicate=True),
    Output("transformer-info-temperature-rise", "children", allow_duplicate=True),
    Output("transformer-info-comprehensive", "children", allow_duplicate=True),
]

@callback(
    info_panel_outputs,
    Input("transformer-inputs-store", "data"),
    Input("losses-store", "data"),
    Input("impulse-store", "data"),
    Input("dieletric-analysis-store", "data"),
    Input("applied-voltage-store", "data"),
    Input("induced-voltage-store", "data"),
    Input("short-circuit-store", "data"),
    Input("temperature-rise-store", "data"),
    prevent_initial_call="initial_duplicate",
)
def global_updates_all_transformer_info_panels(
    transformer_store_data,
    losses_store_data,
    impulse_store_data,
    dieletric_store_data,
    applied_voltage_store_data,
    induced_voltage_store_data,
    short_circuit_store_data,
    temperature_rise_store_data,
):
    """Atualiza todos os painéis de informação do transformador."""
    start_time = time.time()
    
    if not hasattr(app, "state_manager") or app.state_manager is None:
        error_panel = html.Div("Erro: StateManager não inicializado", className="alert alert-danger small")
        return [error_panel] * len(info_panel_outputs)

    # Obter dados frescos do state_manager
    transformer_data = app.state_manager.get("transformer-inputs-store") or {}

    # Verificar dados essenciais
    has_essential_data = all(
        transformer_data.get(field) is not None 
        for field in ["potencia_mva", "tensao_at", "tensao_bt"]
    )
    
    if not has_essential_data:
        log.warning("Dados essenciais ausentes no state_manager")
        panel_html = create_transformer_info_panel({})
    else:
        try:
            panel_html = create_transformer_info_panel(transformer_data)
        except Exception as e:
            log.error(f"Erro ao criar painel: {e}")
            panel_html = html.Div(f"Erro ao exibir informações: {str(e)}", className="alert alert-danger small")

    execution_time = time.time() - start_time
    log.debug(f"Painel atualizado em {round(execution_time * 1000, 2)}ms")
    
    return [panel_html] * len(info_panel_outputs)
