# -*- coding: utf-8 -*-
"""
Módulo de callbacks para a seção de Tensão Induzida.
Utiliza o padrão de registro centralizado de callbacks para evitar problemas com o reloader.
"""
import datetime
import logging

# Usado para cálculos matemáticos como potência
import math

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash import Input, Output, State, dcc, html, no_update
from dash.exceptions import PreventUpdate
from plotly import graph_objects as go

# Importações da aplicação
from components.formatters import format_parameter_value
from components.validators import safe_float

# Configurar logger
log = logging.getLogger(__name__)


# --- Funções Auxiliares ---

# Tabelas de potência magnética e perdas do núcleo
potencia_magnet = {
    (0.5, 50): 0.10,
    (0.5, 60): 0.15,
    (0.5, 100): 0.35,
    (0.5, 120): 0.45,
    (0.5, 150): 0.70,
    (0.5, 200): 1.00,
    (0.5, 240): 1.30,
    (0.6, 50): 0.15,
    (0.6, 60): 0.20,
    (0.6, 100): 0.45,
    (0.6, 120): 0.60,
    (0.6, 150): 0.90,
    (0.6, 200): 1.40,
    (0.6, 240): 1.80,
    (0.7, 50): 0.23,
    (0.7, 60): 0.28,
    (0.7, 100): 0.60,
    (0.7, 120): 0.80,
    (0.7, 150): 1.10,
    (0.7, 200): 1.70,
    (0.7, 240): 2.30,
    (0.8, 50): 0.30,
    (0.8, 60): 0.35,
    (0.8, 100): 0.80,
    (0.8, 120): 1.00,
    (0.8, 150): 1.40,
    (0.8, 200): 2.20,
    (0.8, 240): 3.00,
    (0.9, 50): 0.38,
    (0.9, 60): 0.45,
    (0.9, 100): 0.95,
    (0.9, 120): 1.30,
    (0.9, 150): 1.70,
    (0.9, 200): 2.80,
    (0.9, 240): 3.80,
    (1.0, 50): 0.45,
    (1.0, 60): 0.55,
    (1.0, 100): 1.10,
    (1.0, 120): 1.60,
    (1.0, 150): 2.20,
    (1.0, 200): 3.50,
    (1.0, 240): 4.50,
    (1.1, 50): 0.55,
    (1.1, 60): 0.70,
    (1.1, 100): 1.50,
    (1.1, 120): 2.00,
    (1.1, 150): 2.80,
    (1.1, 200): 4.10,
    (1.1, 240): 5.50,
    (1.2, 50): 0.65,
    (1.2, 60): 0.85,
    (1.2, 100): 2.00,
    (1.2, 120): 2.40,
    (1.2, 150): 3.30,
    (1.2, 200): 5.00,
    (1.2, 240): 6.50,
    (1.3, 50): 0.80,
    (1.3, 60): 1.00,
    (1.3, 100): 2.20,
    (1.3, 120): 2.85,
    (1.3, 150): 3.80,
    (1.3, 200): 6.00,
    (1.3, 240): 7.50,
    (1.4, 50): 0.95,
    (1.4, 60): 1.20,
    (1.4, 100): 2.50,
    (1.4, 120): 3.30,
    (1.4, 150): 4.50,
    (1.4, 200): 7.00,
    (1.4, 240): 9.00,  # <-- Relevant for Mag interpolation
    (1.5, 50): 1.10,
    (1.5, 60): 1.40,
    (1.5, 100): 3.00,
    (1.5, 120): 4.00,
    (1.5, 150): 5.50,
    (1.5, 200): 9.00,
    (1.5, 240): 11.00,  # <-- Relevant for Mag interpolation
    (1.6, 50): 1.30,
    (1.6, 60): 1.60,
    (1.6, 100): 3.50,
    (1.6, 120): 4.80,
    (1.6, 150): 6.50,
    (1.6, 200): 12.00,
    (1.6, 240): 14.00,
    (1.7, 50): 1.60,
    (1.7, 60): 2.00,
    (1.7, 100): 4.00,
    (1.7, 120): 5.50,
    (1.7, 150): 7.00,
    (1.7, 200): 15.00,
    (1.7, 240): 17.00,
}

perdas_nucleo = {
    (0.5, 50): 0.10,
    (0.5, 60): 0.13,
    (0.5, 100): 0.25,
    (0.5, 120): 0.35,
    (0.5, 150): 0.50,
    (0.5, 200): 0.80,
    (0.5, 240): 1.10,
    (0.6, 50): 0.12,
    (0.6, 60): 0.18,
    (0.6, 100): 0.38,
    (0.6, 120): 0.48,
    (0.6, 150): 0.70,
    (0.6, 200): 1.10,
    (0.6, 240): 1.50,
    (0.7, 50): 0.15,
    (0.7, 60): 0.23,
    (0.7, 100): 0.50,
    (0.7, 120): 0.62,
    (0.7, 150): 0.95,
    (0.7, 200): 1.55,
    (0.7, 240): 2.10,
    (0.8, 50): 0.20,
    (0.8, 60): 0.30,
    (0.8, 100): 0.65,
    (0.8, 120): 0.80,
    (0.8, 150): 1.20,
    (0.8, 200): 2.00,
    (0.8, 240): 2.80,
    (0.9, 50): 0.25,
    (0.9, 60): 0.37,
    (0.9, 100): 0.82,
    (0.9, 120): 1.00,
    (0.9, 150): 1.50,
    (0.9, 200): 2.50,
    (0.9, 240): 3.50,
    (1.0, 50): 0.32,
    (1.0, 60): 0.46,
    (1.0, 100): 1.00,
    (1.0, 120): 1.25,
    (1.0, 150): 1.85,
    (1.0, 200): 3.10,
    (1.0, 240): 4.20,
    (1.1, 50): 0.41,
    (1.1, 60): 0.55,
    (1.1, 100): 1.21,
    (1.1, 120): 1.55,
    (1.1, 150): 2.20,
    (1.1, 200): 3.70,
    (1.1, 240): 5.00,
    (1.2, 50): 0.50,
    (1.2, 60): 0.65,
    (1.2, 100): 1.41,
    (1.2, 120): 1.90,
    (1.2, 150): 2.70,
    (1.2, 200): 4.50,
    (1.2, 240): 6.00,
    (1.3, 50): 0.60,
    (1.3, 60): 0.80,
    (1.3, 100): 1.65,
    (1.3, 120): 2.30,
    (1.3, 150): 3.20,
    (1.3, 200): 5.20,
    (1.3, 240): 7.00,
    (1.4, 50): 0.71,
    (1.4, 60): 0.95,
    (1.4, 100): 1.95,
    (1.4, 120): 2.80,
    (1.4, 150): 3.80,
    (1.4, 200): 6.00,
    (1.4, 240): 8.50,  # <-- Relevant for Perdas interpolation
    (1.5, 50): 0.85,
    (1.5, 60): 1.10,
    (1.5, 100): 2.30,
    (1.5, 120): 3.30,
    (1.5, 150): 4.50,
    (1.5, 200): 7.00,
    (1.5, 240): 10.00,  # <-- Relevant for Perdas interpolation
    (1.6, 50): 1.00,
    (1.6, 60): 1.30,
    (1.6, 100): 2.80,
    (1.6, 120): 3.80,
    (1.6, 150): 5.30,
    (1.6, 200): 8.00,
    (1.6, 240): 12.00,
    (1.7, 50): 1.20,
    (1.7, 60): 1.55,
    (1.7, 100): 3.50,
    (1.7, 120): 4.40,
    (1.7, 150): 6.00,
    (1.7, 200): 9.00,
    (1.7, 240): 15.00,
}

# Converter as tabelas para DataFrames para facilitar a interpolação
df_potencia_magnet = pd.DataFrame(list(potencia_magnet.items()), columns=["key", "potencia_magnet"])
df_potencia_magnet[["inducao_nominal", "frequencia_nominal"]] = pd.DataFrame(
    df_potencia_magnet["key"].tolist(), index=df_potencia_magnet.index
)
df_potencia_magnet.drop("key", axis=1, inplace=True)
df_potencia_magnet.set_index(["inducao_nominal", "frequencia_nominal"], inplace=True)

df_perdas_nucleo = pd.DataFrame(list(perdas_nucleo.items()), columns=["key", "perdas_nucleo"])
df_perdas_nucleo[["inducao_nominal", "frequencia_nominal"]] = pd.DataFrame(
    df_perdas_nucleo["key"].tolist(), index=df_perdas_nucleo.index
)
df_perdas_nucleo.drop("key", axis=1, inplace=True)
df_perdas_nucleo.set_index(["inducao_nominal", "frequencia_nominal"], inplace=True)


def register_induced_voltage_callbacks(app_instance):
    """
    Registra todos os callbacks para a seção de Tensão Induzida.

    Esta função deve ser chamada após a definição do layout da aplicação,
    passando a instância do app como parâmetro.
    """
    log.debug("Registrando callbacks de Tensão Induzida")

    @app_instance.callback(
        [
            Output("tipo-transformador", "value"),
            Output("frequencia-teste", "value"),
            Output("capacitancia", "value"),
            # Adicionar outros outputs de input da UI aqui se necessário
        ],
        [
            Input("url", "pathname"),
            Input("transformer-inputs-store", "data"),
            Input("induced-voltage-store", "data")
        ],
        prevent_initial_call=False
    )
    def load_induced_voltage_inputs(pathname, transformer_data_global, induced_data_local):
        from dash import ctx
        from utils.routes import ROUTE_INDUCED_VOLTAGE, normalize_pathname # Movido para dentro para evitar import circular no topo

        triggered_id = ctx.triggered_id
        log.debug(f"[LOAD InducedInputs] Acionado por: {triggered_id}, Pathname: {pathname}")

        clean_path = normalize_pathname(pathname) if pathname else ""
        if clean_path != ROUTE_INDUCED_VOLTAGE and triggered_id == 'url':
            log.debug(f"[LOAD InducedInputs] Não na página de Tensão Induzida ({clean_path}). Abortando trigger de URL.")
            raise PreventUpdate

        # Valores padrão ou do store local
        tipo_trafo_local = None
        freq_teste_local = None
        cap_local = None

        if induced_data_local and isinstance(induced_data_local, dict):
            # Verificar se os dados estão em 'inputs'
            inputs_local = induced_data_local.get('inputs', {}) # Assumindo que os inputs estão em 'inputs'
            if isinstance(inputs_local, dict):
                tipo_trafo_local = inputs_local.get('tipo_transformador')
                freq_teste_local = inputs_local.get('freq_teste') # Nome da chave como no store
                cap_local = inputs_local.get('capacitancia')    # Nome da chave como no store
                log.debug(f"[LOAD InducedInputs] Valores encontrados em 'inputs': tipo_trafo={tipo_trafo_local}, freq_teste={freq_teste_local}, cap={cap_local}")

            # Verificar se os dados estão diretamente no dicionário principal
            if tipo_trafo_local is None and "tipo_transformador" in induced_data_local:
                tipo_trafo_local = induced_data_local.get("tipo_transformador")
                log.debug(f"[LOAD InducedInputs] Valor encontrado diretamente no dicionário principal: tipo_trafo={tipo_trafo_local}")

            if freq_teste_local is None and "freq_teste" in induced_data_local:
                freq_teste_local = induced_data_local.get("freq_teste")
                log.debug(f"[LOAD InducedInputs] Valor encontrado diretamente no dicionário principal: freq_teste={freq_teste_local}")

            if cap_local is None and "capacitancia" in induced_data_local:
                cap_local = induced_data_local.get("capacitancia")
                log.debug(f"[LOAD InducedInputs] Valor encontrado diretamente no dicionário principal: cap={cap_local}")

        # Obter tipo de transformador do store global
        tipo_trafo_global = None
        if transformer_data_global and isinstance(transformer_data_global, dict):
            tipo_trafo_global = transformer_data_global.get('tipo_transformador')

        # Decidir qual valor usar para tipo_transformador
        # Prioriza valor local se existir, senão global, senão padrão
        final_tipo_trafo = tipo_trafo_local if tipo_trafo_local else tipo_trafo_global if tipo_trafo_global else "Monofásico" # Padrão

        # Para frequência de teste e capacitância, priorizar valor local, senão None (ou um padrão se definido)
        final_freq_teste = freq_teste_local # Pode ser None se não estiver no store local
        final_cap = cap_local             # Pode ser None se não estiver no store local

        log.debug(f"[LOAD InducedInputs] Valores para UI: TipoTrafo={final_tipo_trafo}, FreqTeste={final_freq_teste}, Cap={final_cap}")

        # Se o trigger foi a mudança de URL e estamos na página correta, ou se foi a mudança de um dos stores, atualiza.
        # Se o trigger foi a mudança de URL para OUTRA página, o PreventUpdate acima já tratou.
        if (triggered_id == 'url' and clean_path == ROUTE_INDUCED_VOLTAGE) or \
           triggered_id == 'transformer-inputs-store' or \
           triggered_id == 'induced-voltage-store':
            return final_tipo_trafo, final_freq_teste, final_cap

        raise PreventUpdate


    # --- Callback para exibir informações do transformador na página ---
    # Este callback copia o conteúdo do painel global para o painel específico da página
    @app_instance.callback(
        Output("transformer-info-induced-page", "children"),
        Input("transformer-info-induced", "children"),
        prevent_initial_call=False,
    )
    def update_induced_page_info_panel(global_panel_content):
        """Copia o conteúdo do painel global para o painel específico da página."""
        return global_panel_content

    # --- Callback principal para cálculo de tensão induzida ---
    @app_instance.callback(
        [
            Output("resultado-tensao-induzida", "children"),
            Output("induced-voltage-store", "data"),
            Output("induced-voltage-error-message", "children"),
        ],
        Input("calc-induced-voltage-btn", "n_clicks"),
        [
            State("transformer-inputs-store", "data"),
            State("losses-store", "data"),
            State("induced-voltage-store", "data"),
            State("url", "pathname"),
            State("frequencia-teste", "value"),
            State("capacitancia", "value"),
            State("tipo-transformador", "value"),
        ],
        prevent_initial_call=True,
    )
    def calculate_induced_voltage(
        n_clicks,
        transformer_data,
        losses_data,
        current_store_data,
        pathname,
        freq_teste_input,
        capacitancia_input,
        tipo_transformador_input,
    ):
        """Calcula a tensão induzida com base nos dados do transformador e perdas."""
        log.debug(
            f"[Induced Voltage] Callback calculate_induced_voltage: n_clicks={n_clicks}, pathname={pathname}"
        )

        # Obter todos os estados para debug (will only show explicitly listed states)
        # from dash import callback_context
        # ctx = callback_context
        # states = ctx.states if ctx else {}
        # log.debug(f"[Induced Voltage] ESTADOS PASSADOS NO INÍCIO DO CALLBACK: {states}")

        # Verificar se estamos na página de tensão induzida
        from utils.routes import ROUTE_INDUCED_VOLTAGE, normalize_pathname

        # Normaliza o pathname para remover barras extras
        clean_path = normalize_pathname(pathname) if pathname else ""

        # Verifica se estamos na página de tensão induzida
        if clean_path != ROUTE_INDUCED_VOLTAGE:
            log.debug(
                f"[Induced Voltage] Não estamos na página de tensão induzida (pathname={pathname}, clean_path={clean_path}), prevenindo atualização"
            )
            raise PreventUpdate

        # Se não foi um clique no botão, não faz nada
        if n_clicks is None or n_clicks == 0:
            return no_update, no_update, no_update

        # Validar dados de entrada
        if not transformer_data:
            error_msg = html.Div(
                "Erro: Dados do transformador não disponíveis. Preencha a seção de dados básicos do transformador.",
                className="alert alert-danger",
            )
            return no_update, no_update, error_msg

        # Verificar se os dados de perdas estão disponíveis e contém as informações necessárias
        if not losses_data or "resultados_perdas_vazio" not in losses_data:
            error_msg = html.Div(
                "Erro: Dados de perdas em vazio não disponíveis. Por favor, complete a seção de perdas em vazio antes de prosseguir.",
                className="alert alert-danger",
            )
            return no_update, no_update, error_msg

        try:
            # Verificar se os dados do transformador estão aninhados em transformer_data
            if "transformer_data" in transformer_data and isinstance(transformer_data["transformer_data"], dict):
                # Usar os dados aninhados
                transformer_dict = transformer_data["transformer_data"]
                log.debug(f"[Induced Voltage] Usando dados aninhados em transformer_data")
            else:
                # Usar os dados diretamente
                transformer_dict = transformer_data
                log.debug(f"[Induced Voltage] Usando dados diretamente do dicionário principal")

            # --- Obtenção de Dados de Entrada ---
            # Prioritize data from stores, use inputs/states as potential overrides if applicable

            # Frequência nominal (fn)
            freq_nominal = safe_float(transformer_dict.get("frequencia"), 60)  # Default 60 Hz

            # Frequência de teste (fp) - Directly from input parameter
            log.debug(
                f"[Induced Voltage] VALOR OBTIDO DO PARÂMETRO freq_teste_input: {freq_teste_input}, tipo: {type(freq_teste_input)}"
            )
            try:
                if freq_teste_input is not None:
                    if isinstance(freq_teste_input, str):
                        freq_teste_input = freq_teste_input.replace(",", ".")
                    freq_teste = float(freq_teste_input)
                else:
                    freq_teste = None
            except (ValueError, TypeError):
                freq_teste = None
            log.debug(f"[Induced Voltage] Frequência de teste após conversão: {freq_teste}")
            if freq_teste is None or freq_teste <= 0:
                raise ValueError(
                    "A frequência de teste não foi fornecida ou é inválida. Preencha o campo 'Teste (fp)'."
                )

            # Tensão AT (Un_AT)
            tensao_at = safe_float(transformer_data.get("tensao_at", 0), 0)
            if tensao_at <= 0:
                raise ValueError("Tensão AT (Un_AT) não definida ou inválida.")

            # Tensão BT (Un_BT)
            tensao_bt = safe_float(transformer_data.get("tensao_bt", 0), 0)
            if tensao_bt <= 0:
                raise ValueError("Tensão BT (Un_BT) não definida ou inválida.")

            # Tensão de Teste Induzida (Vti)
            tensao_teste_induzida_at = safe_float(transformer_data.get("teste_tensao_induzida_at"), 0)
            if tensao_teste_induzida_at <= 0:
                raise ValueError("Tensão de Teste Induzida AT não definida ou inválida.")

            # Capacitância (C) - Directly from input parameter
            log.debug(
                f"[Induced Voltage] VALOR OBTIDO DO PARÂMETRO capacitancia_input: {capacitancia_input}, tipo: {type(capacitancia_input)}"
            )
            try:
                if capacitancia_input is not None:
                    if isinstance(capacitancia_input, str):
                        capacitancia_input = capacitancia_input.replace(",", ".")
                    capacitancia = float(capacitancia_input)
                else:
                    capacitancia = None
            except (ValueError, TypeError):
                capacitancia = None
            log.debug(f"[Induced Voltage] Capacitância após conversão: {capacitancia}")
            if capacitancia is None or capacitancia <= 0:
                raise ValueError(
                    "Capacitância não fornecida ou inválida. Preencha o campo 'Capacitância (C)'."
                )

            # Tipo de Transformador - Directly from input parameter
            log.debug(
                f"[Induced Voltage] VALOR OBTIDO DO PARÂMETRO tipo_transformador_input: {tipo_transformador_input}"
            )
            tipo_transformador = tipo_transformador_input
            if not tipo_transformador:
                raise ValueError("Tipo de transformador não selecionado.")

            # Obter dados de perdas em vazio
            perdas_vazio_data = losses_data.get("resultados_perdas_vazio", {})
            inducao_nominal = safe_float(perdas_vazio_data.get("inducao_nominal"), 0)
            perda_nucleo_especifica = safe_float(perdas_vazio_data.get("perda_nucleo_especifica"), 0)
            potencia_aparente_especifica = safe_float(perdas_vazio_data.get("potencia_aparente_especifica"), 0)

            if not all([inducao_nominal, perda_nucleo_especifica, potencia_aparente_especifica]):
                raise ValueError("Dados de perdas em vazio incompletos ou inválidos.")

            # --- Cálculos ---
            # Tensão de Teste Induzida (Vti) - já obtida
            # Frequência de Teste (fp) - já obtida
            # Frequência Nominal (fn) - já obtida
            # Capacitância (C) - já obtida
            # Indução Nominal (Bn) - já obtida de perdas_vazio_data
            # Perda Específica do Núcleo (Pn) - já obtida de perdas_vazio_data
            # Potência Aparente Específica (Sn) - já obtida de perdas_vazio_data

            # 1. Indução no Teste (Bp)
            Bp = inducao_nominal * (tensao_teste_induzida_at / tensao_at) * (freq_nominal / freq_teste)

            # 2. Interpolação para obter Pp e Sp na frequência de teste (fp) e indução de teste (Bp)
            # Garantir que Bp e fp estão dentro dos limites das tabelas
            min_inducao = df_potencia_magnet.index.get_level_values("inducao_nominal").min()
            max_inducao = df_potencia_magnet.index.get_level_values("inducao_nominal").max()
            min_freq = df_potencia_magnet.index.get_level_values("frequencia_nominal").min()
            max_freq = df_potencia_magnet.index.get_level_values("frequencia_nominal").max()

            if not (min_inducao <= Bp <= max_inducao and min_freq <= freq_teste <= max_freq):
                raise ValueError(
                    f"Indução de teste ({Bp:.2f} T) ou frequência de teste ({freq_teste} Hz) fora dos limites da tabela de interpolação."
                )

            # Encontrar os vizinhos mais próximos para interpolação bilinear
            inducao_vals = sorted(df_potencia_magnet.index.get_level_values("inducao_nominal").unique())
            freq_vals = sorted(df_potencia_magnet.index.get_level_values("frequencia_nominal").unique())

            # Vizinhos da indução
            i1 = max([i for i in inducao_vals if i <= Bp])
            i2 = min([i for i in inducao_vals if i >= Bp])

            # Vizinhos da frequência
            f1 = max([f for f in freq_vals if f <= freq_teste])
            f2 = min([f for f in freq_vals if f >= freq_teste])

            # Obter os valores das tabelas nos pontos vizinhos
            Q11_mag = df_potencia_magnet.loc[(i1, f1), "potencia_magnet"]
            Q12_mag = df_potencia_magnet.loc[(i1, f2), "potencia_magnet"]
            Q21_mag = df_potencia_magnet.loc[(i2, f1), "potencia_magnet"]
            Q22_mag = df_potencia_magnet.loc[(i2, f2), "potencia_magnet"]

            Q11_per = df_perdas_nucleo.loc[(i1, f1), "perdas_nucleo"]
            Q12_per = df_perdas_nucleo.loc[(i1, f2), "perdas_nucleo"]
            Q21_per = df_perdas_nucleo.loc[(i2, f1), "perdas_nucleo"]
            Q22_per = df_perdas_nucleo.loc[(i2, f2), "perdas_nucleo"]

            # Interpolação Bilinear
            if i1 == i2 and f1 == f2:
                Sp = Q11_mag
                Pp = Q11_per
            elif i1 == i2: # Interpolação linear na frequência
                Sp = Q11_mag + (Q12_mag - Q11_mag) * (freq_teste - f1) / (f2 - f1)
                Pp = Q11_per + (Q12_per - Q11_per) * (freq_teste - f1) / (f2 - f1)
            elif f1 == f2: # Interpolação linear na indução
                Sp = Q11_mag + (Q21_mag - Q11_mag) * (Bp - i1) / (i2 - i1)
                Pp = Q11_per + (Q21_per - Q11_per) * (Bp - i1) / (i2 - i1)
            else: # Interpolação bilinear
                R1_mag = Q11_mag + (Q21_mag - Q11_mag) * (Bp - i1) / (i2 - i1)
                R2_mag = Q12_mag + (Q22_mag - Q12_mag) * (Bp - i1) / (i2 - i1)
                Sp = R1_mag + (R2_mag - R1_mag) * (freq_teste - f1) / (f2 - f1)

                R1_per = Q11_per + (Q21_per - Q11_per) * (Bp - i1) / (i2 - i1)
                R2_per = Q12_per + (Q22_per - Q12_per) * (Bp - i1) / (i2 - i1)
                Pp = R1_per + (R2_per - R1_per) * (freq_teste - f1) / (f2 - f1)

            # 3. Potência Ativa do Núcleo (Pc)
            peso_nucleo = safe_float(transformer_data.get("peso_parte_ativa"), 0) # Assumindo peso da parte ativa como proxy
            if peso_nucleo <= 0:
                raise ValueError("Peso da parte ativa não definido ou inválido.")
            Pc = Pp * peso_nucleo

            # 4. Potência Aparente do Núcleo (Sc)
            Sc = Sp * peso_nucleo

            # 5. Potência Reativa do Núcleo (Qc)
            if Sc**2 < Pc**2:
                 log.warning(f"Sc ({Sc}) ao quadrado é menor que Pc ({Pc}) ao quadrado. Ajustando Qc para 0.")
                 Qc = 0
            else:
                 Qc = math.sqrt(Sc**2 - Pc**2)

            # 6. Potência Reativa Capacitiva (Qcap)
            # Tensão de Teste Induzida (Vti) em Volts
            Vti_volts = tensao_teste_induzida_at * 1000
            # Capacitância (C) em Farads
            C_farads = capacitancia * 1e-9 # Convertendo nF para F
            # Frequência de teste (fp) em Hz
            omega = 2 * math.pi * freq_teste
            Qcap = (Vti_volts**2) * omega * C_farads

            # Ajuste para transformador trifásico
            if tipo_transformador == "Trifásico":
                Qcap *= 3

            # 7. Potência Reativa Total (Qt)
            Qt = Qc - Qcap

            # 8. Potência Ativa Total (Pt)
            Pt = Pc # Potência ativa total é a potência ativa do núcleo

            # 9. Potência Aparente Total (St)
            St = math.sqrt(Pt**2 + Qt**2)

            # 10. Fator de Potência (FP)
            FP = Pt / St if St > 0 else 1 # Evita divisão por zero

            # --- Preparação dos Resultados para Exibição ---
            results_dict = {
                "Tensão de Teste (Vti)": f"{tensao_teste_induzida_at:.2f} kV",
                "Frequência de Teste (fp)": f"{freq_teste:.2f} Hz",
                "Capacitância (C)": f"{capacitancia:.2f} nF",
                "Indução no Teste (Bp)": f"{Bp:.3f} T",
                "Perda Específica Interpolada (Pp)": f"{Pp:.3f} W/kg",
                "Potência Aparente Específica Interpolada (Sp)": f"{Sp:.3f} VA/kg",
                "Peso do Núcleo (Estimado)": f"{peso_nucleo:.2f} kg",
                "Potência Ativa do Núcleo (Pc)": f"{Pc / 1000:.2f} kW",
                "Potência Reativa do Núcleo (Qc)": f"{Qc / 1000:.2f} kVAr",
                "Potência Aparente do Núcleo (Sc)": f"{Sc / 1000:.2f} kVA",
                "Potência Reativa Capacitiva (Qcap)": f"{Qcap / 1000:.2f} kVAr",
                "Potência Reativa Total (Qt)": f"{Qt / 1000:.2f} kVAr",
                "Potência Ativa Total (Pt)": f"{Pt / 1000:.2f} kW",
                "Potência Aparente Total (St)": f"{St / 1000:.2f} kVA",
                "Fator de Potência (FP)": f"{FP:.3f}",
            }

            # Criar DataFrame para a tabela
            df_results = pd.DataFrame(list(results_dict.items()), columns=["Parâmetro", "Valor"])

            # Criar gráfico de barras para potências
            potencias_labels = ["Pc (kW)", "Qc (kVAr)", "Sc (kVA)", "Qcap (kVAr)", "Qt (kVAr)", "Pt (kW)", "St (kVA)"]
            potencias_values = [
                Pc / 1000,
                Qc / 1000,
                Sc / 1000,
                Qcap / 1000,
                Qt / 1000,
                Pt / 1000,
                St / 1000,
            ]

            fig = go.Figure(data=[go.Bar(x=potencias_labels, y=potencias_values)])
            fig.update_layout(
                title="Distribuição das Potências Calculadas",
                xaxis_title="Tipo de Potência",
                yaxis_title="Valor",
                template="plotly_white", # Usar tema claro para o gráfico
            )
            graph_component = dcc.Graph(figure=fig)

            # Montar a tabela de resultados completa
            results_table = dbc.Card(
                [
                    dbc.CardHeader(
                        html.H6(
                            f"Resultados do Cálculo - {tipo_transformador}",
                            className="mb-0 text-dark", # Garante texto escuro
                        )
                    ),
                    dbc.CardBody(
                        [
                            dbc.Table.from_dataframe(
                                df_results, striped=True, bordered=True, hover=True
                            ),
                            graph_component, # Adiciona o gráfico aqui
                        ],
                        className="text-dark", # Garante texto escuro
                    ),
                ],
                color="light",
                className="mt-3",
            )

            # Montar o card de recomendações
            recommendation_content = []
            if Qt > 0:
                recommendation_content.append(
                    html.P(
                        f"A Potência Reativa Total (Qt = {Qt / 1000:.2f} kVAr) é positiva (indutiva). "
                        f"O sistema de excitação pode precisar fornecer essa potência reativa."
                    )
                )
            else:
                recommendation_content.append(
                    html.P(
                        f"A Potência Reativa Total (Qt = {Qt / 1000:.2f} kVAr) é negativa (capacitiva). "
                        f"O sistema de excitação pode precisar absorver essa potência reativa. "
                        f"Considere o uso de reatores se a capacitância for muito alta."
                    )
                )

            if recommendation_content:
                recommendations = dbc.Card(
                    [
                        dbc.CardHeader(
                            html.H6("Recomendações", className="mb-0 text-dark") # Garante texto escuro
                        ),
                        dbc.CardBody(recommendation_content, className="text-dark"), # Garante texto escuro
                    ],
                    color="light", # Modified
                    className="mt-3",
                )
            else:
                recommendations = dbc.Card(
                    [
                        dbc.CardHeader(
                            html.H6("Recomendações", className="mb-0 text-dark") # Garante texto escuro
                        ),
                        dbc.CardBody("Nenhuma recomendação específica.", className="text-dark"), # Garante texto escuro
                    ],
                    color="light", # Modified
                    className="mt-3",
                )

            # Salvar resultados no store local
            new_store_data = current_store_data.copy() if current_store_data else {}
            # Salvar inputs usados no cálculo
            new_store_data['inputs'] = {
                'freq_teste': freq_teste,
                'capacitancia': capacitancia,
                'tipo_transformador': tipo_transformador
            }
            # Salvar resultados calculados
            new_store_data['results'] = results_dict
            # Adicionar timestamp
            new_store_data['last_calculated'] = datetime.datetime.now().isoformat()

            log.debug(f"[Induced Voltage] Novos dados para o store: {new_store_data}")

            # Retornar os resultados e os dados atualizados do store
            return html.Div([results_table, recommendations]), new_store_data, None

        except ValueError as ve:
            log.error(f"[Induced Voltage] Erro de validação: {ve}")
            error_msg = html.Div(f"Erro de Validação: {ve}", className="alert alert-warning")
            return no_update, no_update, error_msg
        except Exception as e:
            log.exception("[Induced Voltage] Erro inesperado durante o cálculo:")
            error_msg = html.Div(f"Erro inesperado: {e}", className="alert alert-danger")
            return no_update, no_update, error_msg

    log.debug("Callbacks de Tensão Induzida registrados com sucesso.")

