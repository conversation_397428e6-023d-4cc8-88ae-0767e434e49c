# callbacks/global_actions.py
"""
Callbacks para ações globais, como limpar todos os campos.
"""
import logging
from dash import Input, Output, State, html, no_update, dcc
from dash.exceptions import PreventUpdate

# Importar estado inicial pad<PERSON><PERSON>
from app_core.transformer_mcp import DEFAULT_TRANSFORMER_INPUTS

log = logging.getLogger(__name__)

def register_global_actions_callbacks(app_instance):
    """
    Registra callbacks para ações globais.
    """
    log.info(f"Registrando callbacks de ações globais para app {app_instance.title}...")

    @app_instance.callback(
        [
            Output("transformer-inputs-store", "data", allow_duplicate=True),
            Output("losses-store", "data", allow_duplicate=True),
            Output("impulse-store", "data", allow_duplicate=True),
            Output("dieletric-analysis-store", "data", allow_duplicate=True),
            Output("applied-voltage-store", "data", allow_duplicate=True),
            Output("induced-voltage-store", "data", allow_duplicate=True),
            Output("short-circuit-store", "data", allow_duplicate=True),
            Output("temperature-rise-store", "data", allow_duplicate=True),
            Output("comprehensive-analysis-store", "data", allow_duplicate=True),
            # Adicionar outros stores que precisam ser limpos, se houver
        ],
        Input("limpar-transformer-inputs", "n_clicks"),
        prevent_initial_call=True,
    )
    def clear_all_inputs(n_clicks):
        """
        Limpa os dados de todos os stores principais da aplicação,
        retornando-os ao estado inicial.
        Acionado pelo botão 'LIMPAR' na seção de Dados Básicos.
        """
        if n_clicks is None or n_clicks == 0:
            raise PreventUpdate

        log.warning(f"Botão Limpar Campos clicado ({n_clicks} vezes). Limpando todos os stores principais.")

        # Estado inicial para cada store
        initial_transformer_inputs = DEFAULT_TRANSFORMER_INPUTS.copy()
        initial_losses = {"resultados_perdas_vazio": {}, "resultados_perdas_carga": {}}
        initial_empty = {}

        # Retorna os estados iniciais para limpar os stores
        return (
            initial_transformer_inputs,
            initial_losses,
            initial_empty, # impulse-store
            initial_empty, # dieletric-analysis-store
            initial_empty, # applied-voltage-store
            initial_empty, # induced-voltage-store
            initial_empty, # short-circuit-store
            initial_empty, # temperature-rise-store
            initial_empty, # comprehensive-analysis-store
        )

    log.info("Callbacks de ações globais registrados.")

