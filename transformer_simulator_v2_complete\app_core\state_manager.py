# app_core/state_manager.py
"""
Módulo centralizador para gerenciamento de estado, persistência e sincronização do MCP e dados do transformador.
Todas as operações de get/set, sincronização e persistência devem passar por aqui.
"""
import logging
from app_core.transformer_mcp import TransformerMCP
from utils.mcp_persistence import ensure_mcp_data_propagation, sync_isolation_values_enhanced

log = logging.getLogger(__name__)

class StateManager:
    def __init__(self, load_from_disk=True):
        self.mcp = TransformerMCP(load_from_disk=load_from_disk)
        log.info("StateManager: MCP inicializado.")

    def get(self, store, key=None):
        data = self.mcp.get_data(store)
        if key is None:
            return data
        return data.get(key) if data else None

    def get_store(self, store):
        """
        Obtém todos os dados de um store.
        Alias para mcp.get_data para compatibilidade com a interface esperada.
        """
        return self.mcp.get_data(store)

    def set(self, store, key, value, auto_propagate=True):
        data = self.mcp.get_data(store) or {}
        data[key] = value
        self.mcp.set_data(store, data, auto_propagate=auto_propagate)
        log.info(f"StateManager: set {store}[{key}] = {value}")

    def set_store(self, store, data, auto_propagate=True):
        """
        Seta o dicionário inteiro de um store (útil para callbacks que já montam o dict completo).
        """
        self.mcp.set_data(store, data, auto_propagate=auto_propagate)
        log.info(f"StateManager: set_store {store} (dict inteiro)")

    def save(self, force=True):
        self.mcp.save_to_disk(force=force)
        log.info("StateManager: MCP salvo em disco.")

    def propagate_all(self, source_store, target_stores):
        # Criar um objeto mock app que contém o state_manager
        class MockApp:
            def __init__(self, state_manager):
                self.state_manager = state_manager
                self.mcp = state_manager.mcp

        mock_app = MockApp(self)
        return ensure_mcp_data_propagation(mock_app, source_store, target_stores)

    def sync_isolation(self):
        # Criar um objeto mock app que contém o state_manager
        class MockApp:
            def __init__(self, state_manager):
                self.state_manager = state_manager
                self.mcp = state_manager.mcp

        mock_app = MockApp(self)
        return sync_isolation_values_enhanced(mock_app)

    def get_mcp(self):
        return self.mcp

# Instância global para uso em todo o app
state_manager = StateManager(load_from_disk=True)
